<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Code Execution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .code-block {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        textarea {
            width: 100%;
            height: 100px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #4caf50;
            background: #f1f8e9;
        }
        .error {
            border-color: #f44336;
            background: #ffebee;
        }
    </style>
</head>
<body>
    <h1>Alt-Notion Code Execution Test</h1>
    
    <div class="code-block">
        <h3>Test Python Code Execution</h3>
        <textarea id="codeInput" placeholder="Enter Python code here...">print("Hello from Alt-Notion!")
import math
print(f"Pi = {math.pi}")
print(f"2 + 2 = {2 + 2}")

# Test list comprehension
numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers]
print(f"Squares: {squares}")
</textarea>
        <br>
        <button id="executeBtn" onclick="executeCode()">Execute Code</button>
        <button onclick="clearResult()">Clear Result</button>
    </div>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        async function executeCode() {
            const code = document.getElementById('codeInput').value;
            const executeBtn = document.getElementById('executeBtn');
            const resultDiv = document.getElementById('result');
            
            if (!code.trim()) {
                alert('Please enter some code to execute');
                return;
            }
            
            executeBtn.disabled = true;
            executeBtn.textContent = 'Executing...';
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Executing code...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('http://localhost:5002/api/code/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code: code })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Success (${result.execution_time.toFixed(4)}s)\n\nOutput:\n${result.output}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error (${result.execution_time.toFixed(4)}s)\n\nOutput:\n${result.output}\n\nErrors:\n${result.errors}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Connection Error:\n${error.message}`;
            } finally {
                executeBtn.disabled = false;
                executeBtn.textContent = 'Execute Code';
            }
        }
        
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
        }
        
        // Allow Ctrl+Enter to execute code
        document.getElementById('codeInput').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                executeCode();
            }
        });
    </script>
</body>
</html>
