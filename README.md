# 📝 Alt-Notion

Uma versão alternativa e simplificada do Notion, construída com Flask (backend) e Vue.js (frontend), com funcionalidades avançadas como execução de código Python inline, integração com LLMs e colaboração em tempo real.

![Alt-Notion](https://img.shields.io/badge/Status-Production%20Ready-green)
![Python](https://img.shields.io/badge/Python-3.11+-blue)
![Vue.js](https://img.shields.io/badge/Vue.js-3.0-green)
![License](https://img.shields.io/badge/License-MIT-blue)

## ⚡ Quick Start

### 🐳 Docker (Recomendado)

```bash
git clone <repository-url>
cd alt-notion
cp .env.example .env
docker-compose --profile development up -d
```

**URLs de Acesso:**
- **Frontend**: http://localhost:8080
- **Backend API**: http://localhost:5001
- **Health Check**: http://localhost:5001/health

## ✨ Funcionalidades Principais

### 📄 Gerenciamento de Páginas
- **Hierarquia infinita** - Páginas e subpáginas organizadas em árvore
- **Títulos editáveis inline** - Clique e edite títulos diretamente
- **Auto-save inteligente** - Salvamento automático com debounce
- **Navegação por breadcrumbs** - Navegação contextual hierárquica
- **Busca global** - Encontre qualquer página rapidamente

### ✏️ Editor Avançado
- **Editor similar ao Notion** - Interface familiar e intuitiva
- **Formatação rica** - Negrito, itálico, títulos, listas, citações
- **Blocos de código executáveis** - Execute Python, JavaScript e mais
- **Blocos matemáticos** - Suporte a LaTeX para fórmulas
- **Comandos slash (/)** - Menu contextual rápido para inserir blocos
- **Atalhos de teclado** - Produtividade maximizada

### 🚀 Execução de Código
- **Python inline** - Execute código Python diretamente nas páginas
- **Múltiplas linguagens** - Python, JavaScript, SQL, Bash
- **Resultados visuais** - Gráficos, tabelas e output formatado
- **Sandbox seguro** - Execução isolada e segura
- **Assistente de código IA** - Sugestões e otimizações inteligentes

### 🤖 Integração com IA
- **OpenRouter API** - Acesso a modelos como GPT-4, Claude
- **Ollama local** - Modelos locais para privacidade
- **Assistente de escrita** - Melhore, resuma, traduza texto
- **Autocompletar inteligente** - Sugestões contextuais
- **Explicação de código** - IA explica seu código

### 🔗 Sistema de Compartilhamento
- **Links públicos únicos** - Compartilhe páginas com qualquer pessoa
- **Proteção por senha** - Links seguros com autenticação
- **Data de expiração** - Controle temporal de acesso
- **Limite de visualizações** - Controle quantitativo de acesso
- **Comentários** - Feedback colaborativo em páginas públicas
- **Analytics básicos** - Acompanhe visualizações e engajamento

### 🔄 Colaboração em Tempo Real
- **WebSocket** - Atualizações instantâneas
- **Presença de usuários** - Veja quem está editando
- **Cursores colaborativos** - Acompanhe edições em tempo real
- **Sincronização automática** - Conflitos resolvidos automaticamente

## 🏗️ Arquitetura

### Backend (Flask)
```
backend/
├── app/
│   ├── models/          # Modelos SQLAlchemy
│   ├── api/            # Blueprints da API REST
│   ├── services/       # Lógica de negócio
│   ├── utils/          # Utilitários e helpers
│   └── extensions.py   # Extensões Flask
├── tests/              # Testes unitários e integração
├── Dockerfile         # Container de produção
└── requirements.txt   # Dependências Python
```

### Frontend (Vue.js)
```
frontend/
├── src/
│   ├── components/     # Componentes Vue reutilizáveis
│   ├── views/         # Páginas/rotas principais
│   ├── stores/        # Estado global (Pinia)
│   ├── composables/   # Hooks reutilizáveis
│   └── router/        # Configuração de rotas
├── tests/             # Testes frontend
├── Dockerfile        # Container de produção
└── package.json      # Dependências Node.js
```

## 🛠️ Tecnologias Utilizadas

### Backend
- **Flask** - Framework web Python
- **SQLAlchemy** - ORM para banco de dados
- **Flask-SocketIO** - WebSocket para tempo real
- **SQLite** - Banco de dados local
- **Python 3.11+** - Linguagem principal

### Frontend
- **Vue.js 3** - Framework JavaScript reativo
- **TypeScript** - Tipagem estática
- **Pinia** - Gerenciamento de estado
- **Vite** - Build tool moderna
- **BlockNote** - Editor de texto rico
- **Socket.io-client** - Cliente WebSocket

## 📦 Instalação e Execução

### 🐳 Com Docker (Recomendado)

```bash
# Clone o repositório
git clone <repository-url>
cd alt-notion

# Configure as variáveis de ambiente
cp .env.example .env

# Execute em desenvolvimento
docker-compose --profile development up -d

# Ou em produção
docker-compose --profile production up -d
```

### 💻 Execução Local

#### Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
PORT=5001 python run.py
```

#### Frontend
```bash
cd frontend
npm install
npm run dev
```

## 🧪 Testes

### Backend
```bash
cd backend
source venv/bin/activate
pytest --cov=app --cov-report=html
```

### Frontend
```bash
cd frontend
npm run test:unit
npm run test:coverage
```

## 📊 Status do Desenvolvimento

### ✅ Completado (95%)
- ✅ Backend Flask com API RESTful completa
- ✅ Modelos de dados (Páginas, Blocos, Compartilhamento)
- ✅ Sistema de execução de código Python
- ✅ Integração com OpenRouter e Ollama
- ✅ Frontend Vue.js com interface moderna
- ✅ Sistema de compartilhamento avançado
- ✅ Editor com blocos de código executáveis
- ✅ WebSocket para colaboração em tempo real
- ✅ Testes unitários e integração
- ✅ Containerização Docker
- ✅ Documentação completa

### 🚧 Em Progresso (5%)
- 🔄 Deploy em produção
- 🔄 Monitoramento e logs
- 🔄 Otimizações de performance

## ⚙️ Configuração

### Variáveis de Ambiente

#### Backend (.env)
```bash
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
OPENROUTER_API_KEY=your-openrouter-key
OLLAMA_BASE_URL=http://localhost:11434
DATABASE_URL=sqlite:///app.db
```

#### Frontend (.env)
```bash
VITE_API_BASE_URL=http://localhost:5001
```

## 📖 Documentação da API

A documentação completa da API está disponível em:
- **Swagger UI**: http://localhost:5001/api/docs
- **Arquivo local**: [docs/API.md](./docs/API.md)

### Principais Endpoints

#### Páginas
- `GET /api/pages` - Listar todas as páginas
- `POST /api/pages` - Criar nova página
- `GET /api/pages/{id}` - Obter página específica
- `PUT /api/pages/{id}` - Atualizar página
- `DELETE /api/pages/{id}` - Deletar página

#### Compartilhamento
- `POST /api/sharing/pages/{id}/share` - Criar link de compartilhamento
- `GET /api/sharing/{token}` - Acessar página compartilhada
- `PUT /api/sharing/{token}` - Atualizar configurações
- `DELETE /api/sharing/{token}` - Remover compartilhamento

#### Execução de Código
- `POST /api/code/execute` - Executar código Python
- `GET /api/code/status/{job_id}` - Status de execução

#### IA/LLM
- `POST /api/ai/complete` - Autocompletar texto
- `POST /api/ai/improve` - Melhorar texto
- `POST /api/ai/explain` - Explicar código

## 🚀 Deployment

### Produção com Docker Compose

```bash
# Configurar ambiente de produção
cp .env.example .env.production

# Editar variáveis para produção
vim .env.production

# Deploy
docker-compose --profile production --env-file .env.production up -d
```

### Nginx (Frontend)

O frontend inclui configuração Nginx otimizada com:
- Compressão gzip
- Cache de assets estáticos
- Proxy para API e WebSocket
- Headers de segurança
- Suporte a SPA

## 🔍 Monitoramento

### Health Checks
```bash
# Backend health
curl http://localhost:5001/health

# Database status
curl http://localhost:5001/api/health/db

# WebSocket status
curl http://localhost:5001/api/health/websocket
```

### Logs
```bash
# Ver logs do backend
docker-compose logs -f backend

# Ver logs do frontend
docker-compose logs -f frontend
```

## 🛡️ Segurança

- **Execução de código em sandbox** - Ambiente isolado para Python
- **Validação de entrada** - Todos os endpoints validados
- **Rate limiting** - Proteção contra abuse
- **CORS configurado** - Controle de origem
- **Headers de segurança** - CSP, HSTS, X-Frame-Options
- **Sanitização de conteúdo** - Proteção contra XSS

## 🗂️ Estrutura de Dados

### Página
```json
{
  "id": "uuid",
  "title": "Título da Página",
  "content": "Conteúdo em JSON",
  "parent_id": "uuid ou null",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### Compartilhamento
```json
{
  "id": "uuid",
  "page_id": "uuid",
  "token": "unique-token",
  "password": "hash ou null",
  "expires_at": "timestamp ou null",
  "view_limit": "integer ou null",
  "view_count": "integer"
}
```

## 🧪 Testes

O projeto inclui uma suíte completa de testes:

### Coverage Atual
- **Backend**: 85%+ cobertura
- **Frontend**: 70%+ cobertura

### Executar Testes
```bash
# Backend
cd backend && pytest --cov=app --cov-report=html

# Frontend
cd frontend && npm run test:coverage

# Testes de integração
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 🗺️ Roadmap

### Versão Atual (v1.0)
- ✅ Editor funcional com blocos
- ✅ Execução de código Python
- ✅ Compartilhamento avançado
- ✅ IA integrada
- ✅ Colaboração tempo real

### Próxima Versão (v1.1)
- 🔄 Sistema de autenticação
- 🔄 Workspaces multi-usuário
- 🔄 Templates de páginas
- 🔄 Exportação (PDF, Markdown)

### Futuro (v2.0)
- 📱 App mobile
- 🔌 Plugins e extensões
- 📊 Analytics avançados
- 🌐 Modo offline

## 🤝 Contribuindo

1. **Fork o projeto**
2. **Crie uma branch** para sua feature (`git checkout -b feature/NovaFuncionalidade`)
3. **Commit suas mudanças** (`git commit -m 'Adiciona nova funcionalidade'`)
4. **Push para a branch** (`git push origin feature/NovaFuncionalidade`)
5. **Abra um Pull Request**

### Guidelines
- Siga os padrões de código existentes
- Adicione testes para novas funcionalidades
- Atualize a documentação quando necessário
- Use commits semânticos

## 📋 Requirements

### Sistema
- **Python 3.11+**
- **Node.js 18+**
- **Docker & Docker Compose** (recomendado)

### Desenvolvimento
- **Git** - Controle de versão
- **VS Code** - IDE recomendada
- **Postman/Insomnia** - Teste de APIs

## ⚠️ Limitações Conhecidas

- Execução de código limitada ao ambiente sandbox
- Colaboração em tempo real pode ter latência em conexões lentas
- Limite de 100MB por página (incluindo assets)
- Máximo de 10 execuções simultâneas de código

## 🆘 Suporte

### FAQ
**P: Como configurar a integração com IA?**
R: Configure `OPENROUTER_API_KEY` no .env ou instale Ollama localmente.

**P: O código Python pode acessar a internet?**
R: Não, a execução é em sandbox isolado por segurança.

**P: Como fazer backup dos dados?**
R: Execute `docker exec backend python backup.py` ou copie o arquivo SQLite.

### Troubleshooting
- **Porta 5001 em uso**: Mude `PORT=5002` no .env
- **WebSocket não conecta**: Verifique firewall e proxies
- **IA não responde**: Confirme chaves de API válidas

## 📄 Licença

Este projeto está licenciado sob a **MIT License** - veja o arquivo [LICENSE](LICENSE) para detalhes.

---

**🚀 Desenvolvido com ❤️ para democratizar o acesso a ferramentas de produtividade poderosas**

*Status: Production Ready | Última atualização: Dezembro 2024*
