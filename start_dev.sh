#!/bin/bash

# Alt-Notion Development Server Starter
echo "🚀 Starting Alt-Notion Development Environment"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo -e "${RED}❌ Please run this script from the alt-notion root directory${NC}"
    exit 1
fi

echo -e "${BLUE}📦 Checking dependencies...${NC}"

# Check Python virtual environment
if [ ! -d "backend/venv" ]; then
    echo -e "${RED}❌ Python virtual environment not found${NC}"
    echo -e "${YELLOW}Please run: cd backend && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt${NC}"
    exit 1
fi

# Check Node modules
if [ ! -d "frontend/node_modules" ]; then
    echo -e "${RED}❌ Node modules not found${NC}"
    echo -e "${YELLOW}Please run: cd frontend && npm install${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Dependencies check passed${NC}"

# Function to start backend
start_backend() {
    echo -e "${BLUE}🔙 Starting Backend Server...${NC}"
    cd backend
    source venv/bin/activate
    echo -e "${GREEN}Backend starting on http://localhost:5000${NC}"
    python run.py &
    BACKEND_PID=$!
    cd ..
}

# Function to start frontend
start_frontend() {
    echo -e "${BLUE}🖼️ Starting Frontend Server...${NC}"
    cd frontend
    echo -e "${GREEN}Frontend starting on http://localhost:5173${NC}"
    npm run dev &
    FRONTEND_PID=$!
    cd ..
}

# Trap to handle script interruption
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down servers...${NC}"
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    echo -e "${GREEN}✅ Servers stopped${NC}"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Start services
start_backend
sleep 3
start_frontend

echo -e "\n${GREEN}🎉 Alt-Notion Development Environment is running!${NC}"
echo -e "${BLUE}📝 Backend API:${NC} http://localhost:5000"
echo -e "${BLUE}🌐 Frontend:${NC} http://localhost:5173"
echo -e "${BLUE}📊 Health Check:${NC} http://localhost:5000/health"
echo -e "\n${YELLOW}Press Ctrl+C to stop all servers${NC}"

# Wait for both processes
wait
