version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: alt-notion-backend
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///data/alt_notion.db}
      - SECRET_KEY=${SECRET_KEY:-change-me-in-production}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://ollama:11434}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:5173}
    volumes:
      - ./backend/data:/app/data
      - ./backend/logs:/app/logs
      - ./backend:/app:delegated  # Only in development
    depends_on:
      - redis
      - ollama
    networks:
      - alt-notion-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Vite development server)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: alt-notion-frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:5001}
      - NODE_ENV=${NODE_ENV:-production}
    volumes:
      - ./frontend:/app:delegated  # Only in development
      - /app/node_modules  # Prevent overwriting node_modules
    depends_on:
      - backend
    networks:
      - alt-notion-network
    restart: unless-stopped

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: alt-notion-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - alt-notion-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama for local LLM inference
  ollama:
    image: ollama/ollama:latest
    container_name: alt-notion-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    networks:
      - alt-notion-network
    restart: unless-stopped
    # GPU support (uncomment if you have GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Nginx reverse proxy (production only)
  nginx:
    image: nginx:alpine
    container_name: alt-notion-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - backend
      - frontend
    networks:
      - alt-notion-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  redis-data:
    driver: local
  ollama-data:
    driver: local

networks:
  alt-notion-network:
    driver: bridge

# Override configurations for different environments
---
# Development profile
x-development: &development
  profiles:
    - development
  build:
    target: development
  environment:
    - FLASK_ENV=development
    - NODE_ENV=development
  volumes:
    - ./backend:/app:delegated
    - ./frontend:/app:delegated
