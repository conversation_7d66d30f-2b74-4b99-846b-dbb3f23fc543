# Multi-stage build for production optimization
FROM python:3.11-slim as builder

# Set work directory
WORKDIR /build

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH"

# Create non-root user
RUN groupadd -r altnotion && useradd -r -g altnotion altnotion

# Install runtime dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy Python dependencies from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY --chown=altnotion:altnotion . .

# Create directories and set permissions
RUN mkdir -p /app/data /app/logs \
    && chown -R altnotion:altnotion /app

# Switch to non-root user
USER altnotion

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5001/api/health || exit 1

# Default command
CMD ["python", "run.py"]

# Development stage (extends production)
FROM production as development

# Switch back to root to install dev dependencies
USER root

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-cov \
    black \
    flake8 \
    mypy

# Install Node.js for frontend integration testing
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Switch back to altnotion user
USER altnotion

# Override command for development
CMD ["python", "run.py"]
