"""Alt-Notion Flask Application Factory"""

import os
from flask import Flask
from dotenv import load_dotenv

from app.extensions import db, cors, socketio
from app.config import Config

# Load environment variables
load_dotenv()

def create_app(config_class=Config):
    """Application factory pattern"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions with app
    db.init_app(app)
    cors.init_app(app, origins=app.config['CORS_ORIGINS'].split(','))
    socketio.init_app(app, cors_allowed_origins=app.config['CORS_ORIGINS'].split(','))
    
    # Register blueprints
    from app.routes.pages import pages_bp
    from app.routes.blocks import blocks_bp
    from app.routes.shares import shares_bp
    from app.routes.llm import llm_bp
    from app.routes.code_execution import code_bp
    from app.routes.agnus import agnus_bp
    
    app.register_blueprint(pages_bp, url_prefix='/api/pages')
    app.register_blueprint(blocks_bp, url_prefix='/api/blocks')
    app.register_blueprint(shares_bp, url_prefix='/api/shares')
    app.register_blueprint(llm_bp, url_prefix='/api/llm')
    app.register_blueprint(code_bp, url_prefix='/api/code')
    app.register_blueprint(agnus_bp, url_prefix='/api/agnus')
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    # Register SocketIO events
    from app.services import socketio_events
    
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        return {'status': 'healthy', 'service': 'alt-notion-api'}
    
    return app
