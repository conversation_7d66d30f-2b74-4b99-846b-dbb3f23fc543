"""Code execution routes"""

from flask import Blueprint, request, jsonify, current_app
from app.services.code_executor import CodeExecutor
from app.models import Block
from app.extensions import db
import ast
import traceback

code_bp = Blueprint('code', __name__)

@code_bp.route('/execute', methods=['POST'])
def execute_code():
    """Execute Python code"""
    try:
        data = request.get_json()
        
        if 'code' not in data:
            return jsonify({'error': 'Code is required'}), 400
        
        code = data['code']
        timeout = data.get('timeout', current_app.config.get('PYTHON_EXECUTION_TIMEOUT', 30))
        
        # Execute code using our secure executor
        executor = CodeExecutor(timeout=timeout)
        result = executor.execute(code)
        
        # If block_id is provided, save result to database
        if 'block_id' in data:
            block = Block.query.get(data['block_id'])
            if block and block.can_execute():
                block.execution_result = result.get('output', '')
                db.session.commit()
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@code_bp.route('/validate', methods=['POST'])
def validate_code():
    """Validate Python code syntax"""
    try:
        data = request.get_json()
        
        if 'code' not in data:
            return jsonify({'error': 'Code is required'}), 400
        
        code = data['code']
        
        try:
            # Try to parse the code
            ast.parse(code)
            return jsonify({
                'valid': True,
                'message': 'Code syntax is valid'
            })
        except SyntaxError as e:
            return jsonify({
                'valid': False,
                'error': str(e),
                'line': e.lineno,
                'column': e.offset
            })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@code_bp.route('/environments', methods=['GET'])
def list_environments():
    """List available execution environments"""
    try:
        return jsonify({
            'environments': [
                {
                    'name': 'python3',
                    'display_name': 'Python 3',
                    'version': '3.x',
                    'available_packages': ['numpy', 'pandas', 'matplotlib', 'requests']
                }
            ],
            'default': 'python3'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
