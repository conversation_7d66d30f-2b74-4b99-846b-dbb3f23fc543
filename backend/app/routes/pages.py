"""Page management routes"""

from flask import Blueprint, request, jsonify
from app.models import Page
from app.extensions import db

pages_bp = Blueprint('pages', __name__)

@pages_bp.route('/', methods=['GET'])
def list_pages():
    """List all root pages"""
    try:
        pages = Page.query.filter_by(parent_id=None).all()
        return jsonify([page.to_dict(include_children=True) for page in pages])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@pages_bp.route('/', methods=['POST'])
def create_page():
    """Create a new page"""
    try:
        data = request.get_json()
        page = Page(
            title=data.get('title', 'Untitled'),
            content=data.get('content', ''),
            parent_id=data.get('parent_id')
        )
        db.session.add(page)
        db.session.commit()
        return jsonify(page.to_dict()), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@pages_bp.route('/<int:page_id>', methods=['GET'])
def get_page(page_id):
    """Get a specific page"""
    try:
        page = Page.query.get_or_404(page_id)
        return jsonify(page.to_dict(include_children=True))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@pages_bp.route('/<int:page_id>', methods=['PUT'])
def update_page(page_id):
    """Update a page"""
    try:
        page = Page.query.get_or_404(page_id)
        data = request.get_json()
        
        if 'title' in data:
            page.title = data['title']
        if 'content' in data:
            page.content = data['content']
        if 'is_public' in data:
            page.is_public = data['is_public']
            
        db.session.commit()
        return jsonify(page.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@pages_bp.route('/<int:page_id>', methods=['DELETE'])
def delete_page(page_id):
    """Delete a page and all its children"""
    try:
        page = Page.query.get_or_404(page_id)
        db.session.delete(page)
        db.session.commit()
        return jsonify({'message': 'Page deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
