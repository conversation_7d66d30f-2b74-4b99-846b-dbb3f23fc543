"""LLM integration routes"""

from flask import Blueprint, request, jsonify, current_app, Response, stream_template
from app.services.llm_service import LLMService
import json

llm_bp = Blueprint('llm', __name__)

@llm_bp.route('/complete', methods=['POST'])
def complete_text():
    """Complete text using LLM"""
    try:
        data = request.get_json()
        
        if 'text' not in data:
            return jsonify({'error': 'Text is required'}), 400
        
        text = data['text']
        context = data.get('context', '')
        max_length = data.get('max_length', 150)
        model = data.get('model', 'ollama')  # 'ollama' or 'openrouter'
        
        llm_service = LLMService()
        result = llm_service.complete_text(
            text=text,
            context=context,
            max_length=max_length,
            model=model
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@llm_bp.route('/suggest', methods=['POST'])
def suggest_text():
    """Suggest text improvements using LLM"""
    try:
        data = request.get_json()
        
        if 'text' not in data:
            return jsonify({'error': 'Text is required'}), 400
        
        text = data['text']
        suggestion_type = data.get('type', 'improve')  # 'improve', 'grammar', 'style'
        model = data.get('model', 'ollama')
        
        llm_service = LLMService()
        result = llm_service.suggest_improvements(
            text=text,
            suggestion_type=suggestion_type,
            model=model
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@llm_bp.route('/stream_complete', methods=['POST'])
def stream_complete():
    """Stream text completion using LLM"""
    try:
        data = request.get_json()
        
        if 'text' not in data:
            return jsonify({'error': 'Text is required'}), 400
        
        text = data['text']
        context = data.get('context', '')
        model = data.get('model', 'ollama')
        
        def generate():
            llm_service = LLMService()
            for chunk in llm_service.stream_complete(
                text=text,
                context=context,
                model=model
            ):
                yield f"data: {json.dumps(chunk)}\n\n"
        
        return Response(
            generate(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@llm_bp.route('/models', methods=['GET'])
def list_models():
    """List available LLM models"""
    try:
        llm_service = LLMService()
        models = llm_service.list_available_models()
        return jsonify(models)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@llm_bp.route('/status', methods=['GET'])
def llm_status():
    """Check LLM services status"""
    try:
        llm_service = LLMService()
        status = llm_service.check_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
