"""Share management routes"""

from flask import Blueprint, request, jsonify
from app.models import Share, Page
from app.extensions import db
from datetime import datetime

shares_bp = Blueprint('shares', __name__)

@shares_bp.route('/', methods=['GET'])
def list_shares():
    """List all shares, optionally filtered by page_id"""
    try:
        page_id = request.args.get('page_id', type=int)
        if page_id:
            shares = Share.query.filter_by(page_id=page_id).all()
        else:
            shares = Share.query.all()
        return jsonify([share.to_dict() for share in shares])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@shares_bp.route('/', methods=['POST'])
def create_share():
    """Create a new share"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if 'page_id' not in data:
            return jsonify({'error': 'page_id is required'}), 400
        
        # Check if page exists
        page = Page.query.get(data['page_id'])
        if not page:
            return jsonify({'error': 'Page not found'}), 404
        
        # Create share using class method
        share = Share.create_share(
            page_id=data['page_id'],
            expires_in_days=data.get('expires_in_days'),
            max_views=data.get('max_views'),
            description=data.get('description')
        )
        
        # Set additional options
        if 'allow_comments' in data:
            share.allow_comments = data['allow_comments']
        if 'password_protected' in data:
            share.password_protected = data['password_protected']
        if 'password_hash' in data:
            share.password_hash = data['password_hash']
        
        db.session.add(share)
        db.session.commit()
        
        response_data = share.to_dict()
        response_data['share_url'] = share.generate_share_url()
        
        return jsonify(response_data), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@shares_bp.route('/<int:share_id>', methods=['GET'])
def get_share(share_id):
    """Get a specific share"""
    try:
        share = Share.query.get_or_404(share_id)
        response_data = share.to_dict()
        response_data['share_url'] = share.generate_share_url()
        return jsonify(response_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@shares_bp.route('/<int:share_id>', methods=['PUT'])
def update_share(share_id):
    """Update a share"""
    try:
        share = Share.query.get_or_404(share_id)
        data = request.get_json()
        
        # Update fields if provided
        if 'is_active' in data:
            share.is_active = data['is_active']
        if 'allow_comments' in data:
            share.allow_comments = data['allow_comments']
        if 'password_protected' in data:
            share.password_protected = data['password_protected']
        if 'password_hash' in data:
            share.password_hash = data['password_hash']
        if 'expires_at' in data:
            if data['expires_at']:
                share.expires_at = datetime.fromisoformat(data['expires_at'])
            else:
                share.expires_at = None
        if 'max_views' in data:
            share.max_views = data['max_views']
        if 'description' in data:
            share.description = data['description']
        
        db.session.commit()
        
        response_data = share.to_dict()
        response_data['share_url'] = share.generate_share_url()
        return jsonify(response_data)
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@shares_bp.route('/<int:share_id>', methods=['DELETE'])
def delete_share(share_id):
    """Delete a share"""
    try:
        share = Share.query.get_or_404(share_id)
        db.session.delete(share)
        db.session.commit()
        return jsonify({'message': 'Share deleted successfully'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@shares_bp.route('/token/<share_token>', methods=['GET'])
def get_shared_page(share_token):
    """Get shared page by token - public endpoint"""
    try:
        share = Share.query.filter_by(share_token=share_token).first()
        if not share:
            return jsonify({'error': 'Share not found'}), 404
        
        if not share.is_valid():
            return jsonify({'error': 'Share is no longer valid'}), 403
        
        # Increment view count
        share.increment_views()
        db.session.commit()
        
        # Get the associated page
        page = share.page
        
        return jsonify({
            'share': {
                'share_token': share.share_token,
                'allow_comments': share.allow_comments,
                'description': share.description,
                'current_views': share.current_views,
                'max_views': share.max_views
            },
            'page': page.to_dict(include_children=True)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@shares_bp.route('/token/<share_token>/validate', methods=['POST'])
def validate_share_password(share_token):
    """Validate share password if required"""
    try:
        share = Share.query.filter_by(share_token=share_token).first()
        if not share:
            return jsonify({'error': 'Share not found'}), 404
        
        if not share.password_protected:
            return jsonify({'valid': True})
        
        data = request.get_json()
        password = data.get('password', '')
        
        # TODO: Implement proper password hashing
        # For now, simple comparison (insecure)
        if share.password_hash == password:
            return jsonify({'valid': True})
        else:
            return jsonify({'valid': False}), 401
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
