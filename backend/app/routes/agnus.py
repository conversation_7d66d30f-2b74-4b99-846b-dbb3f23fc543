"""Agnus Framework integration routes"""

from flask import Blueprint, request, jsonify
from app.utils.agnus_integration import get_agnus_client
from app.models import Page
from app.extensions import db

agnus_bp = Blueprint('agnus', __name__)

@agnus_bp.route('/status', methods=['GET'])
def agnus_status():
    """Check Agnus framework status"""
    try:
        client = get_agnus_client()
        is_configured = client.is_configured()
        
        if is_configured:
            mcps = client.list_available_mcps()
            return jsonify({
                'configured': True,
                'available': True,
                'mcps': mcps
            })
        else:
            return jsonify({
                'configured': False,
                'available': False,
                'message': 'Agnus API key not configured - using mock client'
            })
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agnus_bp.route('/mcps', methods=['GET'])
def list_mcps():
    """List available MCPs"""
    try:
        client = get_agnus_client()
        result = client.list_available_mcps()
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agnus_bp.route('/mcps/execute', methods=['POST'])
def execute_mcp():
    """Execute a specific MCP"""
    try:
        data = request.get_json()
        
        if 'mcp_name' not in data:
            return jsonify({'error': 'mcp_name is required'}), 400
        
        mcp_name = data['mcp_name']
        parameters = data.get('parameters', {})
        
        client = get_agnus_client()
        result = client.execute_mcp(mcp_name, parameters)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agnus_bp.route('/enhance/page/<int:page_id>', methods=['POST'])
def enhance_page(page_id):
    """Enhance a page using Agnus"""
    try:
        page = Page.query.get_or_404(page_id)
        data = request.get_json()
        
        enhancement_type = data.get('type', 'structure')  # structure, content, style
        
        client = get_agnus_client()
        result = client.enhance_page_content(page.content or '', enhancement_type)
        
        if result.get('success'):
            # Optionally save the enhanced content
            if data.get('save_changes', False):
                enhanced_content = result['result'].get('enhanced_content')
                if enhanced_content:
                    page.content = enhanced_content
                    db.session.commit()
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agnus_bp.route('/generate/outline', methods=['POST'])
def generate_outline():
    """Generate page outline using Agnus"""
    try:
        data = request.get_json()
        
        if 'topic' not in data:
            return jsonify({'error': 'topic is required'}), 400
        
        topic = data['topic']
        depth = data.get('depth', 2)
        
        client = get_agnus_client()
        result = client.generate_page_outline(topic, depth)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agnus_bp.route('/analyze/page/<int:page_id>', methods=['POST'])
def analyze_page(page_id):
    """Analyze page content using Agnus"""
    try:
        page = Page.query.get_or_404(page_id)
        
        client = get_agnus_client()
        result = client.analyze_content_structure(page.content or '')
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
