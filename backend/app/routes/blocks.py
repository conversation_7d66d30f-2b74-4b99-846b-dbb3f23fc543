"""Block management routes"""

from flask import Blueprint, request, jsonify
from app.models import Block, Page, BlockType
from app.extensions import db

blocks_bp = Blueprint('blocks', __name__)

@blocks_bp.route('/', methods=['GET'])
def list_blocks():
    """List all blocks, optionally filtered by page_id"""
    try:
        page_id = request.args.get('page_id', type=int)
        if page_id:
            blocks = Block.query.filter_by(page_id=page_id).order_by(Block.order_index).all()
        else:
            blocks = Block.query.order_by(Block.page_id, Block.order_index).all()
        return jsonify([block.to_dict() for block in blocks])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@blocks_bp.route('/', methods=['POST'])
def create_block():
    """Create a new block"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if 'page_id' not in data:
            return jsonify({'error': 'page_id is required'}), 400
        
        # Check if page exists
        page = Page.query.get(data['page_id'])
        if not page:
            return jsonify({'error': 'Page not found'}), 404
        
        # Parse block type
        block_type_str = data.get('block_type', 'text')
        try:
            block_type = BlockType(block_type_str)
        except ValueError:
            return jsonify({'error': f'Invalid block type: {block_type_str}'}), 400
        
        # Create block
        block = Block(
            page_id=data['page_id'],
            block_type=block_type,
            content=data.get('content', ''),
            properties=data.get('properties'),
            code_language=data.get('code_language'),
            is_executable=data.get('is_executable', False),
            order_index=data.get('order_index', 0)
        )
        
        db.session.add(block)
        db.session.commit()
        return jsonify(block.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@blocks_bp.route('/<int:block_id>', methods=['GET'])
def get_block(block_id):
    """Get a specific block"""
    try:
        block = Block.query.get_or_404(block_id)
        return jsonify(block.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@blocks_bp.route('/<int:block_id>', methods=['PUT'])
def update_block(block_id):
    """Update a block"""
    try:
        block = Block.query.get_or_404(block_id)
        data = request.get_json()
        
        # Update fields if provided
        if 'content' in data:
            block.content = data['content']
        if 'properties' in data:
            block.properties = data['properties']
        if 'code_language' in data:
            block.code_language = data['code_language']
        if 'is_executable' in data:
            block.is_executable = data['is_executable']
        if 'order_index' in data:
            block.order_index = data['order_index']
        if 'block_type' in data:
            try:
                block.block_type = BlockType(data['block_type'])
            except ValueError:
                return jsonify({'error': f'Invalid block type: {data["block_type"]}'}), 400
        
        db.session.commit()
        return jsonify(block.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@blocks_bp.route('/<int:block_id>', methods=['DELETE'])
def delete_block(block_id):
    """Delete a block"""
    try:
        block = Block.query.get_or_404(block_id)
        db.session.delete(block)
        db.session.commit()
        return jsonify({'message': 'Block deleted successfully'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@blocks_bp.route('/<int:block_id>/execute', methods=['POST'])
def execute_block(block_id):
    """Execute a code block (placeholder for now)"""
    try:
        block = Block.query.get_or_404(block_id)
        
        if not block.can_execute():
            return jsonify({'error': 'Block cannot be executed'}), 400
        
        # TODO: Implement actual code execution
        # For now, return placeholder result
        result = "Code execution not implemented yet"
        
        # Save execution result
        block.execution_result = result
        db.session.commit()
        
        return jsonify({
            'block_id': block_id,
            'result': result,
            'status': 'placeholder'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
