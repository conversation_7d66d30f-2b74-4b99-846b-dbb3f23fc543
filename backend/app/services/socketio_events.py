"""SocketIO event handlers"""

from flask_socketio import emit
from app.extensions import socketio

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print('Client connected')
    emit('connected', {'message': 'Connected to Alt-Notion server'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')

@socketio.on('join_page')
def handle_join_page(data):
    """Handle client joining a page room for real-time updates"""
    page_id = data.get('page_id')
    if page_id:
        print(f'Client joined page {page_id}')
        emit('joined_page', {'page_id': page_id})

@socketio.on('page_update')
def handle_page_update(data):
    """Handle real-time page updates"""
    # This will be implemented later for real-time collaboration
    emit('page_updated', data, broadcast=True)
