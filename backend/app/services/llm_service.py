"""LLM Service for OpenRouter and Ollama integration"""

import requests
import json
import time
from typing import Dict, Any, Generator, Optional
from flask import current_app

class LLMService:
    """Service for integrating with LLM providers"""
    
    def __init__(self):
        self.openrouter_api_key = current_app.config.get('OPENROUTER_API_KEY')
        self.openrouter_base_url = current_app.config.get('OPENROUTER_BASE_URL')
        self.ollama_base_url = current_app.config.get('OLLAMA_BASE_URL')
        self.ollama_model = current_app.config.get('OLLAMA_MODEL')
    
    def check_status(self) -> Dict[str, Any]:
        """Check status of both LLM services"""
        status = {
            'ollama': {'available': False, 'models': []},
            'openrouter': {'available': False, 'configured': bool(self.openrouter_api_key)}
        }
        
        # Check Ollama
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                status['ollama']['available'] = True
                status['ollama']['models'] = [model['name'] for model in models_data.get('models', [])]
        except:
            pass
        
        # Check OpenRouter (if configured)
        if self.openrouter_api_key:
            try:
                headers = {'Authorization': f'Bearer {self.openrouter_api_key}'}
                response = requests.get(f"{self.openrouter_base_url}/models", headers=headers, timeout=5)
                if response.status_code == 200:
                    status['openrouter']['available'] = True
            except:
                pass
        
        return status
    
    def complete_text(self, text: str, context: str = '', max_length: int = 150, model: str = 'ollama') -> Dict[str, Any]:
        """Complete text using specified LLM provider"""
        try:
            if model == 'ollama':
                return self._complete_with_ollama(text, context, max_length)
            elif model == 'openrouter':
                return self._complete_with_openrouter(text, context, max_length)
            else:
                return {
                    'success': False,
                    'error': f'Unknown model provider: {model}'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def suggest_improvements(self, text: str, suggestion_type: str = 'improve', model: str = 'ollama') -> Dict[str, Any]:
        """Suggest text improvements using LLM"""
        try:
            prompt_templates = {
                'improve': f"Melhore este texto mantendo o significado original:\n\n{text}\n\nTexto melhorado:",
                'grammar': f"Corrija apenas os erros gramaticais neste texto:\n\n{text}\n\nTexto corrigido:",
                'style': f"Reescreva este texto com um estilo mais profissional:\n\n{text}\n\nTexto reescrito:",
                'expand': f"Expanda este texto com mais detalhes e informações relevantes:\n\n{text}\n\nTexto expandido:"
            }
            
            prompt = prompt_templates.get(suggestion_type, prompt_templates['improve'])
            
            if model == 'ollama':
                return self._generate_with_ollama(prompt)
            elif model == 'openrouter':
                return self._generate_with_openrouter(prompt)
            else:
                return {
                    'success': False,
                    'error': f'Unknown model provider: {model}'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def stream_complete(self, text: str, context: str = '', model: str = 'ollama') -> Generator[Dict[str, Any], None, None]:
        """Stream text completion"""
        try:
            if model == 'ollama':
                yield from self._stream_with_ollama(text, context)
            elif model == 'openrouter':
                yield from self._stream_with_openrouter(text, context)
            else:
                yield {'error': f'Unknown model provider: {model}'}
        except Exception as e:
            yield {'error': str(e)}
    
    def list_available_models(self) -> Dict[str, Any]:
        """List all available models from both providers"""
        models = {
            'ollama': [],
            'openrouter': []
        }
        
        # Get Ollama models
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                ollama_data = response.json()
                models['ollama'] = [
                    {
                        'name': model['name'],
                        'size': model.get('size', 'Unknown'),
                        'modified_at': model.get('modified_at')
                    }
                    for model in ollama_data.get('models', [])
                ]
        except:
            pass
        
        # Get OpenRouter models (if configured)
        if self.openrouter_api_key:
            try:
                headers = {'Authorization': f'Bearer {self.openrouter_api_key}'}
                response = requests.get(f"{self.openrouter_base_url}/models", headers=headers, timeout=5)
                if response.status_code == 200:
                    openrouter_data = response.json()
                    models['openrouter'] = [
                        {
                            'name': model['id'],
                            'description': model.get('name', ''),
                            'context_length': model.get('context_length', 0)
                        }
                        for model in openrouter_data.get('data', [])
                    ]
            except:
                pass
        
        return models
    
    def _complete_with_ollama(self, text: str, context: str, max_length: int) -> Dict[str, Any]:
        """Complete text using Ollama"""
        try:
            prompt = f"{context}\n\n{text}" if context else text
            
            payload = {
                'model': self.ollama_model,
                'prompt': prompt + " [CONTINUE]",
                'stream': False,
                'options': {
                    'num_predict': max_length,
                    'temperature': 0.7
                }
            }
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'completion': result.get('response', ''),
                    'model': self.ollama_model,
                    'provider': 'ollama'
                }
            else:
                return {
                    'success': False,
                    'error': f'Ollama API error: {response.status_code}'
                }
                
        except requests.RequestException as e:
            return {
                'success': False,
                'error': f'Ollama connection error: {str(e)}'
            }
    
    def _complete_with_openrouter(self, text: str, context: str, max_length: int) -> Dict[str, Any]:
        """Complete text using OpenRouter"""
        if not self.openrouter_api_key:
            return {
                'success': False,
                'error': 'OpenRouter API key not configured'
            }
        
        try:
            prompt = f"{context}\n\n{text}" if context else text
            
            headers = {
                'Authorization': f'Bearer {self.openrouter_api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': 'anthropic/claude-3.5-sonnet',  # Default model
                'messages': [
                    {
                        'role': 'user',
                        'content': f"Continue este texto de forma natural e coerente:\n\n{prompt}"
                    }
                ],
                'max_tokens': max_length,
                'temperature': 0.7
            }
            
            response = requests.post(
                f"{self.openrouter_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'completion': result['choices'][0]['message']['content'],
                    'model': result.get('model', 'unknown'),
                    'provider': 'openrouter'
                }
            else:
                return {
                    'success': False,
                    'error': f'OpenRouter API error: {response.status_code}'
                }
                
        except requests.RequestException as e:
            return {
                'success': False,
                'error': f'OpenRouter connection error: {str(e)}'
            }
    
    def _generate_with_ollama(self, prompt: str) -> Dict[str, Any]:
        """Generate text using Ollama"""
        try:
            payload = {
                'model': self.ollama_model,
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.7
                }
            }
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'suggestion': result.get('response', ''),
                    'model': self.ollama_model,
                    'provider': 'ollama'
                }
            else:
                return {
                    'success': False,
                    'error': f'Ollama API error: {response.status_code}'
                }
                
        except requests.RequestException as e:
            return {
                'success': False,
                'error': f'Ollama connection error: {str(e)}'
            }
    
    def _generate_with_openrouter(self, prompt: str) -> Dict[str, Any]:
        """Generate text using OpenRouter"""
        if not self.openrouter_api_key:
            return {
                'success': False,
                'error': 'OpenRouter API key not configured'
            }
        
        try:
            headers = {
                'Authorization': f'Bearer {self.openrouter_api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': 'anthropic/claude-3.5-sonnet',
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 500,
                'temperature': 0.7
            }
            
            response = requests.post(
                f"{self.openrouter_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'suggestion': result['choices'][0]['message']['content'],
                    'model': result.get('model', 'unknown'),
                    'provider': 'openrouter'
                }
            else:
                return {
                    'success': False,
                    'error': f'OpenRouter API error: {response.status_code}'
                }
                
        except requests.RequestException as e:
            return {
                'success': False,
                'error': f'OpenRouter connection error: {str(e)}'
            }
    
    def _stream_with_ollama(self, text: str, context: str) -> Generator[Dict[str, Any], None, None]:
        """Stream completion using Ollama"""
        try:
            prompt = f"{context}\n\n{text}" if context else text
            
            payload = {
                'model': self.ollama_model,
                'prompt': prompt + " [CONTINUE]",
                'stream': True,
                'options': {
                    'temperature': 0.7
                }
            }
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk = json.loads(line.decode('utf-8'))
                            if 'response' in chunk:
                                yield {
                                    'success': True,
                                    'chunk': chunk['response'],
                                    'done': chunk.get('done', False),
                                    'model': self.ollama_model,
                                    'provider': 'ollama'
                                }
                        except json.JSONDecodeError:
                            continue
            else:
                yield {
                    'success': False,
                    'error': f'Ollama streaming error: {response.status_code}'
                }
                
        except Exception as e:
            yield {
                'success': False,
                'error': f'Ollama streaming error: {str(e)}'
            }
    
    def _stream_with_openrouter(self, text: str, context: str) -> Generator[Dict[str, Any], None, None]:
        """Stream completion using OpenRouter"""
        if not self.openrouter_api_key:
            yield {
                'success': False,
                'error': 'OpenRouter API key not configured'
            }
            return
        
        try:
            prompt = f"{context}\n\n{text}" if context else text
            
            headers = {
                'Authorization': f'Bearer {self.openrouter_api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': 'anthropic/claude-3.5-sonnet',
                'messages': [
                    {
                        'role': 'user',
                        'content': f"Continue este texto de forma natural:\n\n{prompt}"
                    }
                ],
                'stream': True,
                'max_tokens': 300,
                'temperature': 0.7
            }
            
            response = requests.post(
                f"{self.openrouter_base_url}/chat/completions",
                headers=headers,
                json=payload,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_text = line_text[6:]  # Remove 'data: ' prefix
                            if data_text.strip() == '[DONE]':
                                break
                            try:
                                chunk = json.loads(data_text)
                                if 'choices' in chunk and len(chunk['choices']) > 0:
                                    delta = chunk['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield {
                                            'success': True,
                                            'chunk': delta['content'],
                                            'done': False,
                                            'model': chunk.get('model', 'unknown'),
                                            'provider': 'openrouter'
                                        }
                            except json.JSONDecodeError:
                                continue
                                
                # Send final done message
                yield {
                    'success': True,
                    'chunk': '',
                    'done': True,
                    'provider': 'openrouter'
                }
            else:
                yield {
                    'success': False,
                    'error': f'OpenRouter streaming error: {response.status_code}'
                }
                
        except Exception as e:
            yield {
                'success': False,
                'error': f'OpenRouter streaming error: {str(e)}'
            }

class LLMCache:
    """Simple in-memory cache for LLM responses"""
    
    def __init__(self, max_size: int = 100, ttl: int = 3600):
        self.cache = {}
        self.max_size = max_size
        self.ttl = ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached result"""
        if key in self.cache:
            entry = self.cache[key]
            if time.time() - entry['timestamp'] < self.ttl:
                return entry['value']
            else:
                # Expired entry
                del self.cache[key]
        return None
    
    def set(self, key: str, value: Any):
        """Set cached result"""
        # Simple LRU: remove oldest if cache is full
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]
        
        self.cache[key] = {
            'value': value,
            'timestamp': time.time()
        }
    
    def clear(self):
        """Clear all cached entries"""
        self.cache.clear()

# Global cache instance
llm_cache = LLMCache()
