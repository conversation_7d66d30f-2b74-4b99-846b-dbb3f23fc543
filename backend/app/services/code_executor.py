"""Secure Python code execution service"""

import subprocess
import tempfile
import os
import signal
import sys
from typing import Dict, Any
import time
import json

class CodeExecutor:
    """Secure Python code executor with timeout and sandboxing"""
    
    def __init__(self, timeout: int = 30):
        self.timeout = timeout
        self.allowed_imports = {
            'math', 'random', 'datetime', 'json', 'urllib', 'requests',
            'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
            're', 'collections', 'itertools', 'functools', 'operator'
        }
        self.blocked_imports = {
            'os', 'sys', 'subprocess', 'importlib', 'exec', 'eval',
            'open', '__import__', 'compile', 'globals', 'locals'
        }
    
    def validate_code(self, code: str) -> Dict[str, Any]:
        """Validate code for security issues"""
        # Check for dangerous imports and functions
        dangerous_keywords = [
            'os.', 'sys.', 'subprocess', 'exec(', 'eval(',
            '__import__', 'open(', 'file(', 'input(',
            'raw_input(', 'reload(', 'compile(',
            'globals()', 'locals()', 'vars()', 'dir('
        ]
        
        for keyword in dangerous_keywords:
            if keyword in code:
                return {
                    'valid': False,
                    'error': f'Dangerous operation detected: {keyword}',
                    'code': 'SECURITY_VIOLATION'
                }
        
        return {'valid': True}
    
    def execute(self, code: str) -> Dict[str, Any]:
        """Execute Python code safely with timeout"""
        try:
            # Validate code first
            validation = self.validate_code(code)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error'],
                    'execution_time': 0
                }
            
            # Prepare the code with safety wrapper
            wrapped_code = self._wrap_code(code)
            
            # Execute in subprocess for better isolation
            result = self._execute_subprocess(wrapped_code)
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'output': '',
                'execution_time': 0
            }
    
    def _wrap_code(self, code: str) -> str:
        """Wrap user code with safety measures and output capturing"""
        wrapper = f'''
import sys
import io
import contextlib
import time
import json
import traceback

# Redirect stdout and stderr
original_stdout = sys.stdout
original_stderr = sys.stderr
captured_output = io.StringIO()
captured_errors = io.StringIO()

start_time = time.time()

try:
    # Redirect output
    sys.stdout = captured_output
    sys.stderr = captured_errors
    
    # User code starts here
{self._indent_code(code)}
    # User code ends here
    
    success = True
    error_msg = None
    
except Exception as e:
    success = False
    error_msg = str(e)
    traceback.print_exc()

finally:
    # Restore original stdout/stderr
    sys.stdout = original_stdout
    sys.stderr = original_stderr
    
    execution_time = time.time() - start_time
    
    # Prepare result
    result = {{
        'success': success,
        'output': captured_output.getvalue(),
        'errors': captured_errors.getvalue(),
        'error': error_msg,
        'execution_time': execution_time
    }}
    
    print(json.dumps(result))
'''
        return wrapper
    
    def _indent_code(self, code: str) -> str:
        """Indent user code for inclusion in wrapper"""
        lines = code.split('\n')
        indented_lines = ['    ' + line for line in lines]
        return '\n'.join(indented_lines)
    
    def _execute_subprocess(self, code: str) -> Dict[str, Any]:
        """Execute code in a subprocess with timeout"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Execute the code with timeout
                process = subprocess.Popen(
                    [sys.executable, temp_file],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    preexec_fn=os.setsid  # Create new process group for better cleanup
                )
                
                try:
                    stdout, stderr = process.communicate(timeout=self.timeout)
                    
                    # Try to parse the JSON result from stdout
                    try:
                        result = json.loads(stdout.strip().split('\n')[-1])
                        return result
                    except (json.JSONDecodeError, IndexError):
                        # If JSON parsing fails, return raw output
                        return {
                            'success': process.returncode == 0,
                            'output': stdout,
                            'errors': stderr,
                            'execution_time': 0,
                            'error': 'Could not parse execution result'
                        }
                        
                except subprocess.TimeoutExpired:
                    # Kill the entire process group
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    process.communicate()  # Clean up
                    return {
                        'success': False,
                        'error': f'Code execution timed out after {self.timeout} seconds',
                        'output': '',
                        'execution_time': self.timeout
                    }
                    
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass
                    
        except Exception as e:
            return {
                'success': False,
                'error': f'Execution error: {str(e)}',
                'output': '',
                'execution_time': 0
            }

class SafeCodeEnvironment:
    """Provides a safe environment for code execution with limited imports"""
    
    def __init__(self):
        self.safe_builtins = {
            'abs', 'all', 'any', 'bin', 'bool', 'chr', 'dict', 'enumerate',
            'filter', 'float', 'format', 'frozenset', 'int', 'len', 'list',
            'map', 'max', 'min', 'ord', 'pow', 'print', 'range', 'reversed',
            'round', 'set', 'slice', 'sorted', 'str', 'sum', 'tuple', 'type',
            'zip', 'isinstance', 'issubclass', 'hasattr', 'getattr', 'setattr'
        }
    
    def get_safe_globals(self):
        """Return a dictionary of safe global variables for code execution"""
        safe_globals = {
            '__builtins__': {name: __builtins__[name] for name in self.safe_builtins if name in __builtins__}
        }
        
        # Add safe modules
        try:
            import math
            safe_globals['math'] = math
        except ImportError:
            pass
            
        try:
            import random
            safe_globals['random'] = random
        except ImportError:
            pass
            
        try:
            import datetime
            safe_globals['datetime'] = datetime
        except ImportError:
            pass
        
        return safe_globals
