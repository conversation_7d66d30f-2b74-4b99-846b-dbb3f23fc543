from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Foreign<PERSON>ey, Enum
from sqlalchemy.orm import relationship
from app.extensions import db
import enum

class BlockType(enum.Enum):
    """Enumeration for different block types"""
    TEXT = "text"
    HEADING = "heading"
    CODE = "code"
    IMAGE = "image"
    LIST = "list"
    QUOTE = "quote"
    DIVIDER = "divider"
    EXECUTABLE_CODE = "executable_code"

class Block(db.Model):
    """Model for content blocks within pages"""
    __tablename__ = 'blocks'
    
    id = Column(Integer, primary_key=True)
    page_id = Column(Integer, ForeignKey('pages.id'), nullable=False)
    
    # Block content and type
    block_type = Column(Enum(BlockType), nullable=False, default=BlockType.TEXT)
    content = Column(Text, nullable=True)  # The actual content
    properties = Column(Text, nullable=True)  # JSON properties for styling, etc.
    
    # For executable code blocks
    code_language = Column(String(50), nullable=True)  # python, javascript, etc.
    execution_result = Column(Text, nullable=True)  # Stored execution result
    is_executable = Column(Boolean, default=False)
    
    # Ordering and positioning
    order_index = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    page = relationship('Page', back_populates='blocks')
    
    def __repr__(self):
        return f'<Block {self.block_type.value} on page {self.page_id}>'
    
    def to_dict(self):
        """Convert block to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'page_id': self.page_id,
            'block_type': self.block_type.value,
            'content': self.content,
            'properties': self.properties,
            'code_language': self.code_language,
            'execution_result': self.execution_result,
            'is_executable': self.is_executable,
            'order_index': self.order_index,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def is_code_block(self):
        """Check if this block contains executable code"""
        return self.block_type in [BlockType.CODE, BlockType.EXECUTABLE_CODE]
    
    def can_execute(self):
        """Check if this block can be executed"""
        return self.is_executable and self.code_language == 'python'
