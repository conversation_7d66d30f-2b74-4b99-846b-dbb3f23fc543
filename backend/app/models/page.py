from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.extensions import db

class Page(db.Model):
    """Model for pages with hierarchical structure support"""
    __tablename__ = 'pages'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False, default='Untitled')
    content = Column(Text, nullable=True)  # JSON content from BlockNote
    
    # Hierarchical structure
    parent_id = Column(Integer, ForeignKey('pages.id'), nullable=True)
    order_index = Column(Integer, default=0)  # For ordering siblings
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_public = Column(Boolean, default=False)
    
    # Relationships
    parent = relationship('Page', remote_side=[id], backref='children')
    blocks = relationship('Block', back_populates='page', cascade='all, delete-orphan')
    shares = relationship('Share', back_populates='page', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Page {self.title}>'
    
    def to_dict(self, include_children=False):
        """Convert page to dictionary for JSON serialization"""
        data = {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'parent_id': self.parent_id,
            'order_index': self.order_index,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_public': self.is_public
        }
        
        if include_children:
            data['children'] = [child.to_dict() for child in self.children]
            
        return data
    
    def get_path(self):
        """Get the full path from root to this page"""
        if self.parent:
            return self.parent.get_path() + [self.title]
        return [self.title]
    
    def get_all_descendants(self):
        """Get all descendant pages recursively"""
        descendants = []
        for child in self.children:
            descendants.append(child)
            descendants.extend(child.get_all_descendants())
        return descendants
