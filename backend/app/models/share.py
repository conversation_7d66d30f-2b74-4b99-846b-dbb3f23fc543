from datetime import datetime, timedelta
import uuid
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship
from app.extensions import db

class Share(db.Model):
    """Model for page sharing with unique links"""
    __tablename__ = 'shares'
    
    id = Column(Integer, primary_key=True)
    page_id = Column(Integer, ForeignKey('pages.id'), nullable=False)
    
    # Unique sharing token
    share_token = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    
    # Sharing settings
    is_active = Column(Boolean, default=True)
    allow_comments = Column(Boolean, default=False)
    password_protected = Column(Boolean, default=False)
    password_hash = Column(String(255), nullable=True)
    
    # Access control
    expires_at = Column(DateTime, nullable=True)  # Optional expiration
    max_views = Column(Integer, nullable=True)  # Optional view limit
    current_views = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_accessed = Column(DateTime, nullable=True)
    
    # Optional description for the share
    description = Column(Text, nullable=True)
    
    # Relationships
    page = relationship('Page', back_populates='shares')
    
    def __repr__(self):
        return f'<Share {self.share_token} for page {self.page_id}>'
    
    def to_dict(self):
        """Convert share to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'page_id': self.page_id,
            'share_token': self.share_token,
            'is_active': self.is_active,
            'allow_comments': self.allow_comments,
            'password_protected': self.password_protected,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'max_views': self.max_views,
            'current_views': self.current_views,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_accessed': self.last_accessed.isoformat() if self.last_accessed else None,
            'description': self.description
        }
    
    def is_valid(self):
        """Check if the share is currently valid"""
        if not self.is_active:
            return False
        
        # Check expiration
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
        
        # Check view limit
        if self.max_views and self.current_views >= self.max_views:
            return False
            
        return True
    
    def increment_views(self):
        """Increment view count and update last accessed time"""
        self.current_views += 1
        self.last_accessed = datetime.utcnow()
    
    def generate_share_url(self, base_url="http://localhost:5173"):
        """Generate the full share URL"""
        return f"{base_url}/shared/{self.share_token}"
    
    @classmethod
    def create_share(cls, page_id, expires_in_days=None, max_views=None, description=None):
        """Create a new share for a page"""
        share = cls(
            page_id=page_id,
            description=description,
            max_views=max_views
        )
        
        if expires_in_days:
            share.expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
            
        return share
