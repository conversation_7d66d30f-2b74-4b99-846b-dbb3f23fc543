"""General utility functions and helpers"""

import hashlib
import secrets
import json
from typing import Any, Dict, List, Optional
from datetime import datetime
import re

def generate_secure_token(length: int = 32) -> str:
    """Generate a secure random token"""
    return secrets.token_urlsafe(length)

def hash_password(password: str) -> str:
    """Hash a password using SHA-256 (for simple implementation)"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, password_hash: str) -> bool:
    """Verify a password against its hash"""
    return hash_password(password) == password_hash

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove dangerous characters and normalize
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    sanitized = re.sub(r'[\x00-\x1f]', '', sanitized)  # Remove control characters
    return sanitized[:255]  # Limit length

def parse_json_safe(json_str: Optional[str], default: Any = None) -> Any:
    """Safely parse JSON string, return default if parsing fails"""
    if not json_str:
        return default
    
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def validate_email(email: str) -> bool:
    """Basic email validation"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def clean_html(html_content: str) -> str:
    """Remove potentially dangerous HTML tags"""
    import re
    
    # Remove script tags and their content
    html_content = re.sub(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', html_content, flags=re.IGNORECASE)
    
    # Remove dangerous attributes
    dangerous_attrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
    for attr in dangerous_attrs:
        html_content = re.sub(f'{attr}="[^"]*"', '', html_content, flags=re.IGNORECASE)
        html_content = re.sub(f"{attr}='[^']*'", '', html_content, flags=re.IGNORECASE)
    
    return html_content

def extract_text_from_blocks(blocks: List[Dict[str, Any]]) -> str:
    """Extract plain text from a list of blocks for search/indexing"""
    text_parts = []
    
    for block in blocks:
        content = block.get('content', '')
        block_type = block.get('block_type', 'text')
        
        if block_type in ['text', 'heading', 'quote']:
            # Remove HTML tags for plain text
            clean_text = re.sub(r'<[^>]+>', '', content)
            text_parts.append(clean_text)
        elif block_type == 'code':
            # Include code content but mark it
            text_parts.append(f"[CODE] {content}")
    
    return '\n'.join(text_parts)

def paginate_query(query, page: int = 1, per_page: int = 20, max_per_page: int = 100):
    """Add pagination to SQLAlchemy query"""
    # Ensure per_page doesn't exceed maximum
    per_page = min(per_page, max_per_page)
    
    # Calculate offset
    offset = (page - 1) * per_page
    
    # Apply pagination
    paginated_query = query.offset(offset).limit(per_page)
    
    # Get total count
    total = query.count()
    
    return {
        'items': paginated_query.all(),
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'has_prev': page > 1,
            'has_next': page * per_page < total
        }
    }

def create_response(data: Any = None, message: str = None, success: bool = True, status_code: int = 200) -> tuple:
    """Create standardized API response"""
    response = {
        'success': success,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if data is not None:
        response['data'] = data
    
    if message:
        response['message'] = message
    
    return response, status_code

def validate_page_hierarchy(parent_id: Optional[int], current_page_id: Optional[int] = None) -> bool:
    """Validate that creating a page hierarchy doesn't create cycles"""
    if not parent_id:
        return True
    
    # Import here to avoid circular imports
    from app.models import Page
    
    # Get parent page
    parent = Page.query.get(parent_id)
    if not parent:
        return False
    
    # Check if we're creating a cycle
    if current_page_id:
        current_path = []
        check_page = parent
        
        while check_page:
            if check_page.id == current_page_id:
                return False  # Cycle detected
            current_path.append(check_page.id)
            check_page = check_page.parent
            
            # Prevent infinite loops
            if len(current_path) > 50:
                return False
    
    return True

def get_page_breadcrumbs(page) -> List[Dict[str, Any]]:
    """Get breadcrumb trail for a page"""
    breadcrumbs = []
    current = page
    
    while current:
        breadcrumbs.insert(0, {
            'id': current.id,
            'title': current.title,
            'url': f'/pages/{current.id}'
        })
        current = current.parent
    
    return breadcrumbs
