#!/usr/bin/env python3
"""
Tests for Pages API endpoints
"""

import pytest
import json
from app import create_app
from app.extensions import db
from app.models.page import Page


class TestPagesAPI:
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and database"""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            yield
            db.drop_all()
    
    def test_get_empty_pages_list(self):
        """Test getting empty pages list"""
        response = self.client.get('/api/pages/')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert isinstance(data, list)
        assert len(data) == 0
    
    def test_create_page(self):
        """Test creating a new page"""
        page_data = {
            'title': 'Test Page',
            'content': '<p>This is a test page</p>',
            'is_public': False
        }
        
        response = self.client.post(
            '/api/pages/',
            data=json.dumps(page_data),
            content_type='application/json'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        
        assert data['title'] == 'Test Page'
        assert data['content'] == '<p>This is a test page</p>'
        assert data['is_public'] == False
        assert 'id' in data
        assert 'created_at' in data
        assert 'updated_at' in data
    
    def test_create_page_missing_title(self):
        """Test creating page without title"""
        page_data = {
            'content': '<p>Page without title</p>'
        }
        
        response = self.client.post(
            '/api/pages/',
            data=json.dumps(page_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_get_page(self):
        """Test getting a specific page"""
        # First create a page
        with self.app.app_context():
            page = Page(
                title='Test Page',
                content='<p>Test content</p>',
                is_public=True
            )
            db.session.add(page)
            db.session.commit()
            page_id = page.id
        
        # Now get it
        response = self.client.get(f'/api/pages/{page_id}')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['id'] == page_id
        assert data['title'] == 'Test Page'
        assert data['content'] == '<p>Test content</p>'
        assert data['is_public'] == True
    
    def test_get_nonexistent_page(self):
        """Test getting a page that doesn't exist"""
        response = self.client.get('/api/pages/999')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_update_page(self):
        """Test updating a page"""
        # First create a page
        with self.app.app_context():
            page = Page(
                title='Original Title',
                content='<p>Original content</p>',
                is_public=False
            )
            db.session.add(page)
            db.session.commit()
            page_id = page.id
        
        # Update it
        update_data = {
            'title': 'Updated Title',
            'content': '<p>Updated content</p>',
            'is_public': True
        }
        
        response = self.client.put(
            f'/api/pages/{page_id}',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['title'] == 'Updated Title'
        assert data['content'] == '<p>Updated content</p>'
        assert data['is_public'] == True
    
    def test_delete_page(self):
        """Test deleting a page"""
        # First create a page
        with self.app.app_context():
            page = Page(
                title='Page to Delete',
                content='<p>This will be deleted</p>',
                is_public=False
            )
            db.session.add(page)
            db.session.commit()
            page_id = page.id
        
        # Delete it
        response = self.client.delete(f'/api/pages/{page_id}')
        assert response.status_code == 204
        
        # Try to get it (should fail)
        response = self.client.get(f'/api/pages/{page_id}')
        assert response.status_code == 404
    
    def test_create_child_page(self):
        """Test creating a child page"""
        # First create a parent page
        with self.app.app_context():
            parent = Page(
                title='Parent Page',
                content='<p>Parent content</p>',
                is_public=False
            )
            db.session.add(parent)
            db.session.commit()
            parent_id = parent.id
        
        # Create child page
        child_data = {
            'title': 'Child Page',
            'content': '<p>Child content</p>',
            'parent_id': parent_id,
            'is_public': False
        }
        
        response = self.client.post(
            '/api/pages/',
            data=json.dumps(child_data),
            content_type='application/json'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        
        assert data['title'] == 'Child Page'
        assert data['parent_id'] == parent_id
        assert data['order_index'] == 0
    
    def test_page_hierarchy(self):
        """Test page hierarchy ordering"""
        # Create parent page
        with self.app.app_context():
            parent = Page(
                title='Parent',
                content='<p>Parent</p>',
                is_public=False
            )
            db.session.add(parent)
            db.session.commit()
            parent_id = parent.id
        
        # Create multiple child pages
        for i in range(3):
            child_data = {
                'title': f'Child {i}',
                'content': f'<p>Child {i} content</p>',
                'parent_id': parent_id,
                'order_index': i,
                'is_public': False
            }
            
            response = self.client.post(
                '/api/pages/',
                data=json.dumps(child_data),
                content_type='application/json'
            )
            assert response.status_code == 201
        
        # Get all pages and verify hierarchy
        response = self.client.get('/api/pages/')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        
        # Should have 4 pages total (1 parent + 3 children)
        assert len(data) == 4
        
        # Find parent and children
        parent_page = next((p for p in data if p['id'] == parent_id), None)
        children = [p for p in data if p['parent_id'] == parent_id]
        
        assert parent_page is not None
        assert len(children) == 3
        
        # Children should be ordered correctly
        children.sort(key=lambda x: x['order_index'])
        for i, child in enumerate(children):
            assert child['title'] == f'Child {i}'
            assert child['order_index'] == i


class TestPageValidation:
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and database"""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            yield
            db.drop_all()
    
    def test_invalid_json(self):
        """Test sending invalid JSON"""
        response = self.client.post(
            '/api/pages/',
            data='invalid json',
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_empty_title(self):
        """Test creating page with empty title"""
        page_data = {
            'title': '',
            'content': '<p>Some content</p>'
        }
        
        response = self.client.post(
            '/api/pages/',
            data=json.dumps(page_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_title_too_long(self):
        """Test creating page with title too long"""
        page_data = {
            'title': 'x' * 256,  # Assuming 255 is max length
            'content': '<p>Some content</p>'
        }
        
        response = self.client.post(
            '/api/pages/',
            data=json.dumps(page_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_invalid_parent_id(self):
        """Test creating page with non-existent parent ID"""
        page_data = {
            'title': 'Child Page',
            'content': '<p>Child content</p>',
            'parent_id': 999,  # Non-existent
            'is_public': False
        }
        
        response = self.client.post(
            '/api/pages/',
            data=json.dumps(page_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_circular_hierarchy(self):
        """Test preventing circular page hierarchy"""
        # Create parent page
        with self.app.app_context():
            parent = Page(
                title='Parent',
                content='<p>Parent</p>',
                is_public=False
            )
            db.session.add(parent)
            db.session.commit()
            parent_id = parent.id
        
        # Try to update parent to be child of itself
        update_data = {
            'parent_id': parent_id
        }
        
        response = self.client.put(
            f'/api/pages/{parent_id}',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'circular' in data['error'].lower()


if __name__ == '__main__':
    pytest.main([__file__])
