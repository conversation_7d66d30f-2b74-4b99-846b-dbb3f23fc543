#!/usr/bin/env python3
"""
Tests for Shares API endpoints and shared page access
"""

import pytest
import json
import hashlib
from datetime import datetime, timedelta
from app import create_app
from app.extensions import db
from app.models.page import Page
from app.models.share import Share


class TestSharesAPI:
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and database"""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            # Create a test page
            self.page = Page(
                title='Test Shared Page',
                content='<p>This page will be shared</p>',
                is_public=False
            )
            db.session.add(self.page)
            db.session.commit()
            self.page_id = self.page.id
            yield
            db.drop_all()
    
    def test_create_basic_share(self):
        """Test creating a basic share link"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': False,
            'password_protected': False
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        
        assert data['page_id'] == self.page_id
        assert data['allow_comments'] == False
        assert data['password_protected'] == False
        assert data['is_active'] == True
        assert 'share_token' in data
        assert 'share_url' in data
        assert len(data['share_token']) == 32
    
    def test_create_password_protected_share(self):
        """Test creating a password-protected share"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': True,
            'password_protected': True,
            'password_hash': 'secret123'
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        
        assert data['password_protected'] == True
        assert data['allow_comments'] == True
        # Password hash should not be returned
        assert 'password_hash' not in data
    
    def test_create_share_with_expiration(self):
        """Test creating a share with expiration date"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': False,
            'password_protected': False,
            'expires_in_days': 7
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        
        assert 'expires_at' in data
        # Check that expiration is approximately 7 days from now
        expires_at = datetime.fromisoformat(data['expires_at'].replace('Z', '+00:00'))
        expected_expiry = datetime.utcnow() + timedelta(days=7)
        time_diff = abs((expires_at - expected_expiry).total_seconds())
        assert time_diff < 60  # Within 1 minute tolerance
    
    def test_create_share_with_view_limit(self):
        """Test creating a share with view limit"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': False,
            'password_protected': False,
            'max_views': 10
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 201
        data = json.loads(response.data)
        
        assert data['max_views'] == 10
        assert data['current_views'] == 0
    
    def test_get_shares_for_page(self):
        """Test getting all shares for a page"""
        # First create multiple shares
        with self.app.app_context():
            share1 = Share(
                page_id=self.page_id,
                share_token='token123456789012345678901234567890',
                allow_comments=True,
                password_protected=False,
                is_active=True
            )
            share2 = Share(
                page_id=self.page_id,
                share_token='token234567890123456789012345678901',
                allow_comments=False,
                password_protected=True,
                password_hash=hashlib.sha256('password'.encode()).hexdigest(),
                is_active=False
            )
            db.session.add(share1)
            db.session.add(share2)
            db.session.commit()
        
        response = self.client.get(f'/api/shares/?page_id={self.page_id}')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 2
        
        # Check that shares are returned with correct data
        tokens = [share['share_token'] for share in data]
        assert 'token123456789012345678901234567890' in tokens
        assert 'token234567890123456789012345678901' in tokens
    
    def test_update_share(self):
        """Test updating a share"""
        # Create a share first
        with self.app.app_context():
            share = Share(
                page_id=self.page_id,
                share_token='token123456789012345678901234567890',
                allow_comments=False,
                password_protected=False,
                is_active=True
            )
            db.session.add(share)
            db.session.commit()
            share_id = share.id
        
        # Update it
        update_data = {
            'allow_comments': True,
            'is_active': False
        }
        
        response = self.client.put(
            f'/api/shares/{share_id}',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data['allow_comments'] == True
        assert data['is_active'] == False
    
    def test_delete_share(self):
        """Test deleting a share"""
        # Create a share first
        with self.app.app_context():
            share = Share(
                page_id=self.page_id,
                share_token='token123456789012345678901234567890',
                allow_comments=False,
                password_protected=False,
                is_active=True
            )
            db.session.add(share)
            db.session.commit()
            share_id = share.id
        
        # Delete it
        response = self.client.delete(f'/api/shares/{share_id}')
        assert response.status_code == 204
        
        # Verify it's gone
        response = self.client.get(f'/api/shares/?page_id={self.page_id}')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 0
    
    def test_create_share_invalid_page(self):
        """Test creating share for non-existent page"""
        share_data = {
            'page_id': 999,  # Non-existent
            'allow_comments': False,
            'password_protected': False
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data


class TestSharedPageAccess:
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and database"""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            
            # Create a test page
            self.page = Page(
                title='Shared Test Page',
                content='<h1>Shared Content</h1><p>This is shared content</p>',
                is_public=False
            )
            db.session.add(self.page)
            db.session.commit()
            
            # Create an active share
            self.share = Share(
                page_id=self.page.id,
                share_token='abcdef123456789012345678901234567890',
                allow_comments=True,
                password_protected=False,
                is_active=True
            )
            db.session.add(self.share)
            db.session.commit()
            
            self.token = self.share.share_token
            yield
            db.drop_all()
    
    def test_access_shared_page(self):
        """Test accessing a shared page with valid token"""
        response = self.client.post(
            f'/api/shared/{self.token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert 'page' in data
        assert 'share' in data
        
        assert data['page']['title'] == 'Shared Test Page'
        assert data['page']['content'] == '<h1>Shared Content</h1><p>This is shared content</p>'
        assert data['share']['allow_comments'] == True
        assert data['share']['current_views'] == 1
    
    def test_access_invalid_token(self):
        """Test accessing with invalid token"""
        response = self.client.post(
            '/api/shared/invalidtoken123456789012345678',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_access_inactive_share(self):
        """Test accessing inactive share"""
        # Deactivate the share
        with self.app.app_context():
            share = Share.query.filter_by(share_token=self.token).first()
            share.is_active = False
            db.session.commit()
        
        response = self.client.post(
            f'/api/shared/{self.token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 403
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_access_expired_share(self):
        """Test accessing expired share"""
        # Set expiration to past date
        with self.app.app_context():
            share = Share.query.filter_by(share_token=self.token).first()
            share.expires_at = datetime.utcnow() - timedelta(days=1)
            db.session.commit()
        
        response = self.client.post(
            f'/api/shared/{self.token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 403
        data = json.loads(response.data)
        assert 'error' in data
        assert 'expired' in data['error'].lower()
    
    def test_access_view_limit_exceeded(self):
        """Test accessing share with view limit exceeded"""
        # Set view limit and exceed it
        with self.app.app_context():
            share = Share.query.filter_by(share_token=self.token).first()
            share.max_views = 2
            share.current_views = 2
            db.session.commit()
        
        response = self.client.post(
            f'/api/shared/{self.token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 403
        data = json.loads(response.data)
        assert 'error' in data
        assert 'limit' in data['error'].lower()
    
    def test_password_protected_access(self):
        """Test accessing password-protected share"""
        # Create password-protected share
        with self.app.app_context():
            protected_share = Share(
                page_id=self.page.id,
                share_token='protected123456789012345678901234567',
                allow_comments=False,
                password_protected=True,
                password_hash=hashlib.sha256('secret123'.encode()).hexdigest(),
                is_active=True
            )
            db.session.add(protected_share)
            db.session.commit()
            protected_token = protected_share.share_token
        
        # Try without password
        response = self.client.post(
            f'/api/shared/{protected_token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert 'error' in data
        assert 'password' in data['error'].lower()
        
        # Try with wrong password
        response = self.client.post(
            f'/api/shared/{protected_token}',
            data=json.dumps({'password': 'wrongpassword'}),
            content_type='application/json'
        )
        
        assert response.status_code == 401
        
        # Try with correct password
        response = self.client.post(
            f'/api/shared/{protected_token}',
            data=json.dumps({'password': 'secret123'}),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'page' in data
        assert 'share' in data
    
    def test_view_count_increment(self):
        """Test that view count increments on each access"""
        # First access
        response = self.client.post(
            f'/api/shared/{self.token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['share']['current_views'] == 1
        
        # Second access
        response = self.client.post(
            f'/api/shared/{self.token}',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['share']['current_views'] == 2


class TestShareValidation:
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and database"""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            
            # Create a test page
            self.page = Page(
                title='Test Page',
                content='<p>Test content</p>',
                is_public=False
            )
            db.session.add(self.page)
            db.session.commit()
            self.page_id = self.page.id
            yield
            db.drop_all()
    
    def test_missing_page_id(self):
        """Test creating share without page_id"""
        share_data = {
            'allow_comments': False,
            'password_protected': False
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_invalid_expires_in_days(self):
        """Test creating share with invalid expiration days"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': False,
            'password_protected': False,
            'expires_in_days': -1  # Invalid
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_invalid_max_views(self):
        """Test creating share with invalid max views"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': False,
            'password_protected': False,
            'max_views': 0  # Invalid
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_password_protected_without_password(self):
        """Test creating password-protected share without password"""
        share_data = {
            'page_id': self.page_id,
            'allow_comments': False,
            'password_protected': True
            # Missing password_hash
        }
        
        response = self.client.post(
            '/api/shares/',
            data=json.dumps(share_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'password' in data['error'].lower()


if __name__ == '__main__':
    pytest.main([__file__])
