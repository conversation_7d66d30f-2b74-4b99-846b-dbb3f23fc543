# Alt-Notion Configuration

# Flask Configuration
FLASK_APP=app
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///alt_notion.db

# LLM Configuration
# OpenRouter Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1

# Agnus Framework Configuration
AGNUS_API_KEY=your-agnus-api-key-here

# CORS Configuration
CORS_ORIGINS=http://localhost:5173

# Server Configuration
HOST=0.0.0.0
PORT=5000

# Code Execution Settings
PYTHON_EXECUTION_TIMEOUT=30
MAX_CODE_EXECUTION_TIME=60
