from flask import Flask, request, jsonify
from flask_cors import CORS
import sys
import io

app = Flask(__name__)
CORS(app) # Allow frontend to communicate with this backend

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy'})

@app.route('/execute', methods=['POST'])
def execute_code():
    code = request.json.get('code', '')
    
    # Redirect stdout to capture the output of print()
    old_stdout = sys.stdout
    sys.stdout = captured_output = io.StringIO()
    
    try:
        # Execute the code
        exec(code)
        output = captured_output.getvalue()
    except Exception as e:
        output = f"Error: {str(e)}"
    finally:
        # Restore stdout
        sys.stdout = old_stdout
        
    return jsonify({'output': output.strip()})

if __name__ == '__main__':
    app.run(port=5001, debug=True)

