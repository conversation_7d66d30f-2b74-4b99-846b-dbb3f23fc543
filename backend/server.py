from flask import Flask, request, jsonify
from flask_cors import CORS
import sys
import io
import traceback
import time

app = Flask(__name__)
CORS(app) # Allow frontend to communicate with this backend

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy'})

@app.route('/execute', methods=['POST'])
def execute_code():
    code = request.json.get('code', '')

    # Redirect stdout to capture the output of print()
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    sys.stdout = captured_output = io.StringIO()
    sys.stderr = captured_errors = io.StringIO()

    start_time = time.time()

    try:
        # Execute the code
        exec(code)
        output = captured_output.getvalue()
        errors = captured_errors.getvalue()
        success = True
    except Exception as e:
        output = captured_output.getvalue()
        errors = f"Error: {str(e)}\n{traceback.format_exc()}"
        success = False
    finally:
        # Restore stdout and stderr
        sys.stdout = old_stdout
        sys.stderr = old_stderr

    execution_time = time.time() - start_time

    return jsonify({
        'success': success,
        'output': output.strip() if output else '',
        'errors': errors.strip() if errors else '',
        'execution_time': execution_time
    })

@app.route('/api/code/execute', methods=['POST'])
def execute_code_api():
    """API endpoint compatible with the frontend"""
    return execute_code()

if __name__ == '__main__':
    app.run(port=5002, debug=True, host='0.0.0.0')

