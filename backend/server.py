from flask import Flask, request, jsonify
from flask_cors import CORS
import sys
import io
import traceback
import time
import base64
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import numpy as np

app = Flask(__name__)
CORS(app) # Allow frontend to communicate with this backend

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy'})

def capture_matplotlib_plots():
    """Capture all matplotlib figures as base64 images"""
    plots = []

    # Get all current figures
    figures = [plt.figure(i) for i in plt.get_fignums()]

    for fig in figures:
        # Save figure to bytes buffer
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
        img_buffer.seek(0)

        # Convert to base64
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        plots.append({
            'type': 'image',
            'format': 'png',
            'data': img_base64
        })

        # Close the figure to free memory
        plt.close(fig)

    return plots

@app.route('/execute', methods=['POST'])
def execute_code():
    code = request.json.get('code', '')

    # Clear any existing matplotlib figures
    plt.close('all')

    # Redirect stdout to capture the output of print()
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    sys.stdout = captured_output = io.StringIO()
    sys.stderr = captured_errors = io.StringIO()

    start_time = time.time()
    plots = []

    try:
        # Execute the code
        exec(code)
        output = captured_output.getvalue()
        errors = captured_errors.getvalue()

        # Capture any matplotlib plots
        plots = capture_matplotlib_plots()

        success = True
    except Exception as e:
        output = captured_output.getvalue()
        errors = f"Error: {str(e)}\n{traceback.format_exc()}"
        success = False
    finally:
        # Restore stdout and stderr
        sys.stdout = old_stdout
        sys.stderr = old_stderr

    execution_time = time.time() - start_time

    return jsonify({
        'success': success,
        'output': output.strip() if output else '',
        'errors': errors.strip() if errors else '',
        'execution_time': execution_time,
        'plots': plots
    })

@app.route('/api/code/execute', methods=['POST'])
def execute_code_api():
    """API endpoint compatible with the frontend"""
    return execute_code()

if __name__ == '__main__':
    app.run(port=5002, debug=True, host='0.0.0.0')

