2025-08-27 14:13:37,939 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:13:37,940 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("pages")
2025-08-27 14:13:37,940 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-08-27 14:13:37,941 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("blocks")
2025-08-27 14:13:37,941 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-08-27 14:13:37,941 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("shares")
2025-08-27 14:13:37,941 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-08-27 14:13:37,941 INFO sqlalchemy.engine.Engine COMMIT
🚀 Starting Alt-Notion server on 0.0.0.0:5001
📝 Environment: development
🔗 Frontend CORS: http://localhost:5173
💾 Database: sqlite:///alt_notion.db
2025-08-27 14:13:44,890 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:13:44,902 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:13:44,902 INFO sqlalchemy.engine.Engine [generated in 0.00147s] ()
2025-08-27 14:13:44,911 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:40:49,155 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:40:49,162 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:40:49,162 INFO sqlalchemy.engine.Engine [cached since 1052s ago] ()
2025-08-27 14:40:49,170 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:40:59,764 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:40:59,776 INFO sqlalchemy.engine.Engine INSERT INTO pages (title, content, parent_id, order_index, created_at, updated_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-08-27 14:40:59,777 INFO sqlalchemy.engine.Engine [generated in 0.00122s] ('Página de Teste', '<p>Este é um teste do Alt-Notion!</p>', None, 0, '2025-08-27 17:40:59.776470', '2025-08-27 17:40:59.776478', 0)
2025-08-27 14:40:59,780 INFO sqlalchemy.engine.Engine COMMIT
2025-08-27 14:40:59,782 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:40:59,785 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 14:40:59,785 INFO sqlalchemy.engine.Engine [generated in 0.00032s] (1,)
2025-08-27 14:40:59,788 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:41:04,730 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:41:04,731 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:41:04,731 INFO sqlalchemy.engine.Engine [cached since 1068s ago] ()
2025-08-27 14:41:04,732 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:41:04,732 INFO sqlalchemy.engine.Engine [generated in 0.00028s] (1,)
2025-08-27 14:41:04,735 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:41:25,970 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:41:25,973 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:41:25,974 INFO sqlalchemy.engine.Engine [cached since 1089s ago] ()
2025-08-27 14:41:25,981 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:41:25,981 INFO sqlalchemy.engine.Engine [cached since 21.25s ago] (1,)
2025-08-27 14:41:25,983 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:42:03,771 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:03,778 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:42:03,780 INFO sqlalchemy.engine.Engine [cached since 1127s ago] ()
2025-08-27 14:42:03,797 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:42:03,798 INFO sqlalchemy.engine.Engine [cached since 59.07s ago] (1,)
2025-08-27 14:42:03,809 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:42:12,750 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:12,751 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:42:12,752 INFO sqlalchemy.engine.Engine [cached since 1136s ago] ()
2025-08-27 14:42:12,754 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:42:12,754 INFO sqlalchemy.engine.Engine [cached since 68.02s ago] (1,)
2025-08-27 14:42:12,755 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:42:14,937 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:14,939 INFO sqlalchemy.engine.Engine INSERT INTO pages (title, content, parent_id, order_index, created_at, updated_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-08-27 14:42:14,939 INFO sqlalchemy.engine.Engine [cached since 75.17s ago] ('Nova Página', '', None, 0, '2025-08-27 17:42:14.938849', '2025-08-27 17:42:14.938874', 0)
2025-08-27 14:42:14,941 INFO sqlalchemy.engine.Engine COMMIT
2025-08-27 14:42:14,943 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:14,944 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 14:42:14,944 INFO sqlalchemy.engine.Engine [cached since 75.16s ago] (2,)
2025-08-27 14:42:14,944 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:42:38,045 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:38,047 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:42:38,047 INFO sqlalchemy.engine.Engine [cached since 1161s ago] ()
2025-08-27 14:42:38,049 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:42:38,050 INFO sqlalchemy.engine.Engine [cached since 93.32s ago] (1,)
2025-08-27 14:42:38,050 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:42:38,051 INFO sqlalchemy.engine.Engine [cached since 93.32s ago] (2,)
2025-08-27 14:42:38,052 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:42:45,938 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:45,941 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:42:45,941 INFO sqlalchemy.engine.Engine [cached since 1169s ago] ()
2025-08-27 14:42:45,944 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:42:45,945 INFO sqlalchemy.engine.Engine [cached since 101.2s ago] (1,)
2025-08-27 14:42:45,947 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:42:45,949 INFO sqlalchemy.engine.Engine [cached since 101.2s ago] (2,)
2025-08-27 14:42:45,953 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:42:47,294 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:47,294 INFO sqlalchemy.engine.Engine INSERT INTO pages (title, content, parent_id, order_index, created_at, updated_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-08-27 14:42:47,295 INFO sqlalchemy.engine.Engine [cached since 107.5s ago] ('Nova Página', '', None, 0, '2025-08-27 17:42:47.294790', '2025-08-27 17:42:47.294802', 0)
2025-08-27 14:42:47,296 INFO sqlalchemy.engine.Engine COMMIT
2025-08-27 14:42:47,298 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:42:47,298 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 14:42:47,298 INFO sqlalchemy.engine.Engine [cached since 107.5s ago] (3,)
2025-08-27 14:42:47,299 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:43:58,434 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:43:58,439 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:43:58,440 INFO sqlalchemy.engine.Engine [cached since 1242s ago] ()
2025-08-27 14:43:58,448 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:43:58,448 INFO sqlalchemy.engine.Engine [cached since 173.7s ago] (1,)
2025-08-27 14:43:58,450 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:43:58,450 INFO sqlalchemy.engine.Engine [cached since 173.7s ago] (2,)
2025-08-27 14:43:58,451 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:43:58,451 INFO sqlalchemy.engine.Engine [cached since 173.7s ago] (3,)
2025-08-27 14:43:58,455 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:44:06,067 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:44:06,070 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:44:06,071 INFO sqlalchemy.engine.Engine [cached since 1249s ago] ()
2025-08-27 14:44:06,078 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:44:06,078 INFO sqlalchemy.engine.Engine [cached since 181.4s ago] (1,)
2025-08-27 14:44:06,079 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:44:06,079 INFO sqlalchemy.engine.Engine [cached since 181.4s ago] (2,)
2025-08-27 14:44:06,079 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:44:06,079 INFO sqlalchemy.engine.Engine [cached since 181.4s ago] (3,)
2025-08-27 14:44:06,081 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:44:10,555 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:44:10,567 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:44:10,568 INFO sqlalchemy.engine.Engine [cached since 1254s ago] ()
2025-08-27 14:44:10,571 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:44:10,572 INFO sqlalchemy.engine.Engine [cached since 185.8s ago] (1,)
2025-08-27 14:44:10,573 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:44:10,573 INFO sqlalchemy.engine.Engine [cached since 185.8s ago] (2,)
2025-08-27 14:44:10,575 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:44:10,575 INFO sqlalchemy.engine.Engine [cached since 185.8s ago] (3,)
2025-08-27 14:44:10,576 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 14:45:09,878 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 14:45:09,882 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 14:45:09,883 INFO sqlalchemy.engine.Engine [cached since 1313s ago] ()
2025-08-27 14:45:09,893 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:45:09,893 INFO sqlalchemy.engine.Engine [cached since 245.2s ago] (1,)
2025-08-27 14:45:09,895 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:45:09,895 INFO sqlalchemy.engine.Engine [cached since 245.2s ago] (2,)
2025-08-27 14:45:09,898 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 14:45:09,898 INFO sqlalchemy.engine.Engine [cached since 245.2s ago] (3,)
2025-08-27 14:45:09,906 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 20:30:36,200 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 20:30:36,213 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 20:30:36,214 INFO sqlalchemy.engine.Engine [cached since 1.595e+04s ago] ()
2025-08-27 20:30:36,243 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 20:30:36,244 INFO sqlalchemy.engine.Engine [cached since 1.488e+04s ago] (1,)
2025-08-27 20:30:36,245 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 20:30:36,245 INFO sqlalchemy.engine.Engine [cached since 1.488e+04s ago] (2,)
2025-08-27 20:30:36,246 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 20:30:36,247 INFO sqlalchemy.engine.Engine [cached since 1.488e+04s ago] (3,)
2025-08-27 20:30:36,258 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 20:30:38,018 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 20:30:38,022 INFO sqlalchemy.engine.Engine INSERT INTO pages (title, content, parent_id, order_index, created_at, updated_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-08-27 20:30:38,022 INFO sqlalchemy.engine.Engine [cached since 1.489e+04s ago] ('Nova Página', '', None, 0, '2025-08-27 23:30:38.022502', '2025-08-27 23:30:38.022517', 0)
2025-08-27 20:30:38,026 INFO sqlalchemy.engine.Engine COMMIT
2025-08-27 20:30:38,030 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 20:30:38,030 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 20:30:38,030 INFO sqlalchemy.engine.Engine [cached since 1.489e+04s ago] (4,)
2025-08-27 20:30:38,031 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 20:30:38,276 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 20:30:38,282 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 20:30:38,282 INFO sqlalchemy.engine.Engine [generated in 0.00086s] (4,)
2025-08-27 20:30:38,283 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 20:30:38,284 INFO sqlalchemy.engine.Engine [cached since 1.488e+04s ago] (4,)
2025-08-27 20:30:38,284 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 21:08:25,863 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:25,874 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 21:08:25,875 INFO sqlalchemy.engine.Engine [cached since 1.737e+04s ago] ()
2025-08-27 21:08:25,890 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:25,891 INFO sqlalchemy.engine.Engine [cached since 1.63e+04s ago] (1,)
2025-08-27 21:08:25,891 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:25,891 INFO sqlalchemy.engine.Engine [cached since 1.63e+04s ago] (2,)
2025-08-27 21:08:25,892 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:25,892 INFO sqlalchemy.engine.Engine [cached since 1.63e+04s ago] (3,)
2025-08-27 21:08:25,893 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:25,893 INFO sqlalchemy.engine.Engine [cached since 1.63e+04s ago] (4,)
2025-08-27 21:08:25,897 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 21:08:25,910 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:25,912 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 21:08:25,912 INFO sqlalchemy.engine.Engine [cached since 1421s ago] (4,)
2025-08-27 21:08:25,913 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:25,914 INFO sqlalchemy.engine.Engine [cached since 1.63e+04s ago] (4,)
2025-08-27 21:08:25,914 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 21:08:29,929 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:29,934 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.parent_id IS NULL
2025-08-27 21:08:29,934 INFO sqlalchemy.engine.Engine [cached since 1.737e+04s ago] ()
2025-08-27 21:08:29,938 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:29,938 INFO sqlalchemy.engine.Engine [cached since 1.631e+04s ago] (1,)
2025-08-27 21:08:29,939 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:29,939 INFO sqlalchemy.engine.Engine [cached since 1.631e+04s ago] (2,)
2025-08-27 21:08:29,940 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:29,940 INFO sqlalchemy.engine.Engine [cached since 1.631e+04s ago] (3,)
2025-08-27 21:08:29,940 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:29,940 INFO sqlalchemy.engine.Engine [cached since 1.631e+04s ago] (4,)
2025-08-27 21:08:29,944 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 21:08:33,754 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:33,756 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 21:08:33,756 INFO sqlalchemy.engine.Engine [cached since 1429s ago] (1,)
2025-08-27 21:08:33,758 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE ? = pages.parent_id
2025-08-27 21:08:33,759 INFO sqlalchemy.engine.Engine [cached since 1.631e+04s ago] (1,)
2025-08-27 21:08:33,760 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 21:08:39,142 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:39,143 INFO sqlalchemy.engine.Engine INSERT INTO pages (title, content, parent_id, order_index, created_at, updated_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-08-27 21:08:39,143 INFO sqlalchemy.engine.Engine [cached since 1.632e+04s ago] ('Nova Página', '', None, 0, '2025-08-28 00:08:39.143747', '2025-08-28 00:08:39.143761', 0)
2025-08-27 21:08:39,146 INFO sqlalchemy.engine.Engine COMMIT
2025-08-27 21:08:39,148 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:39,148 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 21:08:39,148 INFO sqlalchemy.engine.Engine [cached since 1.632e+04s ago] (5,)
2025-08-27 21:08:39,149 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-27 21:08:43,501 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:43,503 INFO sqlalchemy.engine.Engine INSERT INTO pages (title, content, parent_id, order_index, created_at, updated_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-08-27 21:08:43,503 INFO sqlalchemy.engine.Engine [cached since 1.633e+04s ago] ('Nova Subpágina', '', 5, 0, '2025-08-28 00:08:43.503112', '2025-08-28 00:08:43.503143', 0)
2025-08-27 21:08:43,505 INFO sqlalchemy.engine.Engine COMMIT
2025-08-27 21:08:43,507 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-27 21:08:43,507 INFO sqlalchemy.engine.Engine SELECT pages.id AS pages_id, pages.title AS pages_title, pages.content AS pages_content, pages.parent_id AS pages_parent_id, pages.order_index AS pages_order_index, pages.created_at AS pages_created_at, pages.updated_at AS pages_updated_at, pages.is_public AS pages_is_public 
FROM pages 
WHERE pages.id = ?
2025-08-27 21:08:43,507 INFO sqlalchemy.engine.Engine [cached since 1.633e+04s ago] (6,)
2025-08-27 21:08:43,509 INFO sqlalchemy.engine.Engine ROLLBACK
