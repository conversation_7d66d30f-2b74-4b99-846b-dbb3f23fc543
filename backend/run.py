#!/usr/bin/env python3
"""
Alt-Notion Server Entry Point
"""

import os
from app import create_app, socketio

# Create Flask application
app = create_app()

if __name__ == '__main__':
    # Get configuration from environment
    host = app.config.get('HOST', '0.0.0.0')
    port = app.config.get('PORT', 5000)
    debug = app.config.get('DEBUG', False)
    
    print(f"🚀 Starting Alt-Notion server on {host}:{port}")
    print(f"📝 Environment: {app.config.get('FLASK_ENV', 'production')}")
    print(f"🔗 Frontend CORS: {app.config.get('CORS_ORIGINS')}")
    print(f"💾 Database: {app.config.get('SQLALCHEMY_DATABASE_URI')}")
    
    # Run with SocketIO support
    socketio.run(
        app,
        host=host,
        port=port,
        debug=debug,
        allow_unsafe_werkzeug=True  # For development only
    )
