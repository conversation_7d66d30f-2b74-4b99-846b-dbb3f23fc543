# Alt-Notion API Documentation 📚

## Base URL
- **Development**: `http://localhost:5000`
- **Health Check**: `GET /health`

## Authentication
Currently using simple token-based authentication for shares. JWT implementation planned for full authentication system.

---

## 📄 Pages API (`/api/pages`)

### List Pages
- **GET** `/api/pages/`
- Lists all root pages (pages without parent)
- **Response**: Array of page objects with children

### Create Page
- **POST** `/api/pages/`
- **Body**:
```json
{
  "title": "Page Title",
  "content": "Page content (JSON from BlockNote)",
  "parent_id": null  // Optional: ID of parent page
}
```

### Get Page
- **GET** `/api/pages/{page_id}`
- Returns page with all children and metadata

### Update Page
- **PUT** `/api/pages/{page_id}`
- **Body**:
```json
{
  "title": "Updated Title",
  "content": "Updated content",
  "is_public": true
}
```

### Delete Page
- **DELETE** `/api/pages/{page_id}`
- Deletes page and all children recursively

---

## 🧱 Blocks API (`/api/blocks`)

### List Blocks
- **GET** `/api/blocks/`
- **Query Params**: `?page_id={id}` (optional filter)
- Lists blocks, optionally filtered by page

### Create Block
- **POST** `/api/blocks/`
- **Body**:
```json
{
  "page_id": 1,
  "block_type": "text|heading|code|executable_code|image|list|quote|divider",
  "content": "Block content",
  "properties": "{\"style\": \"h1\"}",  // Optional JSON
  "code_language": "python",  // For code blocks
  "is_executable": true,  // For executable code blocks
  "order_index": 0
}
```

### Update Block
- **PUT** `/api/blocks/{block_id}`
- Same body structure as creation

### Execute Block
- **POST** `/api/blocks/{block_id}/execute`
- Executes Python code in executable blocks
- **Response**: Execution result with output/errors

---

## 🔗 Shares API (`/api/shares`)

### List Shares
- **GET** `/api/shares/`
- **Query Params**: `?page_id={id}` (optional filter)

### Create Share
- **POST** `/api/shares/`
- **Body**:
```json
{
  "page_id": 1,
  "expires_in_days": 30,  // Optional
  "max_views": 100,  // Optional
  "allow_comments": true,
  "password_protected": false,
  "description": "Share description"
}
```
- **Response**: Share object with `share_url`

### Get Shared Page (Public)
- **GET** `/api/shares/token/{share_token}`
- Public endpoint for accessing shared pages
- Increments view count automatically

### Validate Share Password
- **POST** `/api/shares/token/{share_token}/validate`
- **Body**:
```json
{
  "password": "share_password"
}
```

---

## 🤖 LLM API (`/api/llm`)

### Complete Text
- **POST** `/api/llm/complete`
- **Body**:
```json
{
  "text": "Text to complete",
  "context": "Additional context",  // Optional
  "max_length": 150,
  "model": "ollama|openrouter"  // Default: ollama
}
```

### Suggest Improvements
- **POST** `/api/llm/suggest`
- **Body**:
```json
{
  "text": "Text to improve",
  "type": "improve|grammar|style|expand",  // Default: improve
  "model": "ollama|openrouter"
}
```

### Stream Completion
- **POST** `/api/llm/stream_complete`
- **Content-Type**: `text/event-stream`
- Real-time streaming of text completion

### List Models
- **GET** `/api/llm/models`
- Lists available models from both Ollama and OpenRouter

### LLM Status
- **GET** `/api/llm/status`
- Check connectivity and status of LLM services

---

## 🐍 Code Execution API (`/api/code`)

### Execute Code
- **POST** `/api/code/execute`
- **Body**:
```json
{
  "code": "print('Hello World')",
  "timeout": 30,  // Optional, max execution time
  "block_id": 123  // Optional, saves result to block
}
```
- **Response**:
```json
{
  "success": true,
  "output": "Hello World\n",
  "errors": "",
  "execution_time": 0.002
}
```

### Validate Code
- **POST** `/api/code/validate`
- **Body**:
```json
{
  "code": "print('test')"
}
```
- Validates Python syntax without execution

### List Environments
- **GET** `/api/code/environments`
- Lists available execution environments and packages

---

## ⚙️ Agnus Framework API (`/api/agnus`)

### Agnus Status
- **GET** `/api/agnus/status`
- Check Agnus framework status and available MCPs

### List MCPs
- **GET** `/api/agnus/mcps`
- List all available Model Context Protocols

### Execute MCP
- **POST** `/api/agnus/mcps/execute`
- **Body**:
```json
{
  "mcp_name": "content_enhancer",
  "parameters": {
    "content": "Content to enhance",
    "enhancement_type": "structure"
  }
}
```

### Enhance Page
- **POST** `/api/agnus/enhance/page/{page_id}`
- **Body**:
```json
{
  "type": "structure|content|style",
  "save_changes": false  // Whether to save enhanced content
}
```

### Generate Outline
- **POST** `/api/agnus/generate/outline`
- **Body**:
```json
{
  "topic": "Machine Learning",
  "depth": 2  // Outline depth
}
```

### Analyze Page
- **POST** `/api/agnus/analyze/page/{page_id}`
- Analyze page content structure and readability

---

## 🔌 WebSocket Events

Connect to: `ws://localhost:5000`

### Client Events
- `connect` - Establish connection
- `join_page` - Join page room for updates
  ```json
  {"page_id": 123}
  ```
- `page_update` - Send page updates
  ```json
  {"page_id": 123, "content": "..."}
  ```

### Server Events
- `connected` - Connection confirmation
- `joined_page` - Page room joined
- `page_updated` - Real-time page updates

---

## 📊 Data Models

### Page
```json
{
  "id": 1,
  "title": "Page Title",
  "content": "{...}",  // BlockNote JSON
  "parent_id": null,
  "order_index": 0,
  "created_at": "2024-08-27T12:00:00Z",
  "updated_at": "2024-08-27T12:00:00Z",
  "is_public": false,
  "children": [...]  // Child pages array
}
```

### Block
```json
{
  "id": 1,
  "page_id": 1,
  "block_type": "text",
  "content": "Block content",
  "properties": "{...}",  // JSON properties
  "code_language": "python",
  "execution_result": "Output...",
  "is_executable": true,
  "order_index": 0,
  "created_at": "2024-08-27T12:00:00Z",
  "updated_at": "2024-08-27T12:00:00Z"
}
```

### Share
```json
{
  "id": 1,
  "page_id": 1,
  "share_token": "uuid-token",
  "is_active": true,
  "allow_comments": false,
  "password_protected": false,
  "expires_at": "2024-09-27T12:00:00Z",
  "max_views": 100,
  "current_views": 25,
  "description": "Share description",
  "share_url": "http://localhost:5173/shared/uuid-token"
}
```

---

## 🚨 Error Responses

All endpoints return consistent error format:
```json
{
  "error": "Error message",
  "status_code": 400
}
```

Common status codes:
- `400` - Bad Request (validation error)
- `404` - Not Found
- `500` - Internal Server Error
- `501` - Not Implemented (placeholder endpoints)

---

## 🔧 Configuration

Required environment variables:
- `SECRET_KEY` - Flask secret key
- `DATABASE_URL` - SQLite database path
- `CORS_ORIGINS` - Allowed origins for CORS
- `OPENROUTER_API_KEY` - OpenRouter API key (optional)
- `OLLAMA_BASE_URL` - Ollama server URL (default: localhost:11434)
- `AGNUS_API_KEY` - Agnus framework API key (optional)

---

## 📝 Usage Examples

### Creating a Page with Code Block
```bash
# 1. Create a page
curl -X POST http://localhost:5000/api/pages/ \
  -H "Content-Type: application/json" \
  -d '{"title": "My Python Notebook", "content": ""}'

# 2. Add executable code block
curl -X POST http://localhost:5000/api/blocks/ \
  -H "Content-Type: application/json" \
  -d '{
    "page_id": 1,
    "block_type": "executable_code",
    "content": "print(\"Hello from Alt-Notion!\")",
    "code_language": "python",
    "is_executable": true
  }'

# 3. Execute the code
curl -X POST http://localhost:5000/api/blocks/1/execute
```

### Creating a Share
```bash
curl -X POST http://localhost:5000/api/shares/ \
  -H "Content-Type: application/json" \
  -d '{
    "page_id": 1,
    "expires_in_days": 7,
    "allow_comments": true,
    "description": "Sharing my notebook"
  }'
```

### Using LLM for Text Completion
```bash
curl -X POST http://localhost:5000/api/llm/complete \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Machine learning is",
    "model": "ollama",
    "max_length": 100
  }'
```
