// Script de teste automático para verificar funcionalidades
console.log('🧪 Iniciando testes de funcionalidade...\n');

// Teste 1: Verificar se Vue está carregado
function testVueLoaded() {
    console.log('Teste 1: Vue carregado');
    if (typeof window !== 'undefined' && window.__VUE__) {
        console.log('✅ Vue detectado');
        return true;
    } else {
        console.log('❌ Vue não detectado');
        return false;
    }
}

// Teste 2: Verificar se a aplicação foi montada
function testAppMounted() {
    console.log('Teste 2: Aplicação montada');
    const app = document.querySelector('#app');
    if (app && app.innerHTML.trim() !== '') {
        console.log('✅ Aplicação montada');
        return true;
    } else {
        console.log('❌ Aplicação não montada');
        return false;
    }
}

// Teste 3: Verificar se o editor está renderizado
function testEditorRendered() {
    console.log('Teste 3: Editor renderizado');
    const editor = document.querySelector('.working-editor');
    if (editor) {
        console.log('✅ Editor encontrado');
        return true;
    } else {
        console.log('❌ Editor não encontrado');
        return false;
    }
}

// Teste 4: Verificar se o botão de adicionar bloco existe
function testAddBlockButton() {
    console.log('Teste 4: Botão "Adicionar primeiro bloco"');
    const button = document.querySelector('.btn-large');
    if (button && button.textContent.includes('Adicionar primeiro bloco')) {
        console.log('✅ Botão encontrado');
        return true;
    } else {
        console.log('❌ Botão não encontrado');
        return false;
    }
}

// Teste 5: Simular clique no botão
function testButtonClick() {
    console.log('Teste 5: Simulando clique no botão');
    const button = document.querySelector('.btn-large');
    if (button) {
        try {
            button.click();
            // Aguardar um pouco para o Vue reagir
            setTimeout(() => {
                const blocks = document.querySelectorAll('.block-wrapper');
                if (blocks.length > 0) {
                    console.log('✅ Bloco criado após clique');
                } else {
                    console.log('❌ Nenhum bloco criado');
                }
            }, 500);
            return true;
        } catch (error) {
            console.log('❌ Erro ao clicar:', error);
            return false;
        }
    } else {
        console.log('❌ Botão não encontrado para clique');
        return false;
    }
}

// Executar testes
function runTests() {
    console.log('='.repeat(50));
    console.log('📊 RESULTADOS DOS TESTES');
    console.log('='.repeat(50));
    
    let passed = 0;
    let total = 0;
    
    const tests = [
        testAppMounted,
        testEditorRendered,
        testAddBlockButton,
        testButtonClick
    ];
    
    tests.forEach((test, index) => {
        total++;
        if (test()) {
            passed++;
        }
        console.log('');
    });
    
    console.log('='.repeat(50));
    console.log(`📈 RESULTADO FINAL: ${passed}/${total} testes passaram`);
    console.log(passed === total ? '🎉 TODOS OS TESTES PASSARAM!' : '⚠️  Alguns testes falharam');
    console.log('='.repeat(50));
}

// Aguardar a página carregar completamente antes de executar os testes
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runTests, 1000);
    });
} else {
    setTimeout(runTests, 1000);
}

// Exportar para uso manual
window.runFunctionalityTests = runTests;
console.log('💡 Dica: Execute "runFunctionalityTests()" no console para rodar os testes novamente');
