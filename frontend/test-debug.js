// Script de teste para debug da aplicação Vue
// Simula os problemas encontrados

console.log('=== TESTE DEBUG ===');

// 1. Teste do estado vazio dos blocos
const blocks = [];
console.log('Estado inicial dos blocos:', blocks.length === 0);

// 2. Teste da função addBlock simulada
function createTestBlock(type) {
  return {
    id: 'test-' + Math.random(),
    type: type,
    content: {
      text: '',
      subtype: type === 'text' ? 'paragraph' : type
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

function testAddBlock(type) {
  console.log('--- Teste addBlock ---');
  console.log('addInitialBlock called');
  console.log('addBlock called with type:', type, 'index:', undefined);
  
  const block = createTestBlock(type);
  console.log('created block:', block);
  
  blocks.push(block);
  console.log('blocks after adding:', blocks.length);
  
  return block;
}

// Executa o teste
const testBlock = testAddBlock('text');

// 3. Teste se o bloco seria renderizado
console.log('--- Teste de renderização ---');
console.log('Bloco seria renderizado?', blocks.length > 0);
console.log('Tipo do bloco:', testBlock.type);
console.log('Subtype do conteúdo:', testBlock.content.subtype);

// 4. Teste do TextBlock
console.log('--- Teste TextBlock ---');
console.log('blockType computado seria:', testBlock.content.subtype || 'paragraph');
console.log('Deveria mostrar input de texto?', ['paragraph', 'heading1', 'heading2', 'quote'].includes(testBlock.content.subtype || 'paragraph'));

console.log('=== FIM DO TESTE ===');
