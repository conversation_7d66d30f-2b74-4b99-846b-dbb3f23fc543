<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<style>
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #ffffff;
  color: #37352f;
  line-height: 1.5;
}

#app {
  min-height: 100vh;
}

kbd {
  background-color: #f7f6f3;
  border: 1px solid #e9e9e7;
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2), 0 2px 0 0 rgba(255, 255, 255, 0.7) inset;
  color: #333;
  display: inline-block;
  font-size: 0.85em;
  font-weight: 700;
  line-height: 1;
  padding: 2px 4px;
  white-space: nowrap;
}

/* Notion-like typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin: 0;
  color: #37352f;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(55, 53, 47, 0.16);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(55, 53, 47, 0.24);
}
</style>
