import { ref, onMounted, onUnmounted, readonly } from 'vue'
import { io, type Socket } from 'socket.io-client'

interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
  sender?: string
}

interface PageUpdateMessage {
  type: 'page_updated'
  data: {
    page_id: number
    changes: {
      title?: string
      content?: string
      updated_at: string
    }
    user_id?: string
  }
}

interface UserPresenceMessage {
  type: 'user_presence'
  data: {
    page_id: number
    users: Array<{
      id: string
      name: string
      cursor_position?: number
      last_seen: string
    }>
  }
}

interface CodeExecutionMessage {
  type: 'code_execution'
  data: {
    page_id: number
    block_id: number
    result: {
      output?: string
      error?: string
      execution_time: number
    }
  }
}

type SocketMessage = PageUpdateMessage | UserPresenceMessage | CodeExecutionMessage

export function useWebSocket() {
  const socket = ref<Socket | null>(null)
  const connected = ref(false)
  const error = ref<string>('')
  const lastMessage = ref<WebSocketMessage | null>(null)
  
  // Message handlers
  const messageHandlers = new Map<string, Array<(data: any) => void>>()
  
  // Connect to WebSocket server
  function connect() {
    if (socket.value?.connected) return
    
    try {
      socket.value = io('ws://localhost:5001', {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000
      })
      
      setupSocketListeners()
      
    } catch (err) {
      error.value = 'Failed to connect to WebSocket server'
      console.error('WebSocket connection error:', err)
    }
  }
  
  // Disconnect from WebSocket server
  function disconnect() {
    if (socket.value) {
      socket.value.disconnect()
      socket.value = null
      connected.value = false
    }
  }
  
  // Setup socket event listeners
  function setupSocketListeners() {
    if (!socket.value) return
    
    socket.value.on('connect', () => {
      connected.value = true
      error.value = ''
      console.log('✅ WebSocket connected')
    })
    
    socket.value.on('disconnect', (reason) => {
      connected.value = false
      console.log('❌ WebSocket disconnected:', reason)
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        setTimeout(connect, 1000)
      }
    })
    
    socket.value.on('connect_error', (err) => {
      error.value = `Connection error: ${err.message}`
      connected.value = false
      console.error('WebSocket connection error:', err)
    })
    
    socket.value.on('reconnect', (attemptNumber) => {
      console.log(`✅ WebSocket reconnected after ${attemptNumber} attempts`)
      connected.value = true
      error.value = ''
    })
    
    socket.value.on('reconnect_error', (err) => {
      console.error('WebSocket reconnection error:', err)
    })
    
    // Handle incoming messages
    socket.value.on('message', (message: SocketMessage) => {
      handleIncomingMessage(message)
    })
    
    // Specific event handlers
    socket.value.on('page_updated', (data: PageUpdateMessage['data']) => {
      handleIncomingMessage({ type: 'page_updated', data, timestamp: Date.now() })
    })
    
    socket.value.on('user_presence', (data: UserPresenceMessage['data']) => {
      handleIncomingMessage({ type: 'user_presence', data, timestamp: Date.now() })
    })
    
    socket.value.on('code_execution', (data: CodeExecutionMessage['data']) => {
      handleIncomingMessage({ type: 'code_execution', data, timestamp: Date.now() })
    })
  }
  
  // Handle incoming messages and route to registered handlers
  function handleIncomingMessage(message: SocketMessage) {
    lastMessage.value = {
      type: message.type,
      data: message.data,
      timestamp: Date.now()
    }
    
    const handlers = messageHandlers.get(message.type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.data)
        } catch (err) {
          console.error(`Error in message handler for ${message.type}:`, err)
        }
      })
    }
  }
  
  // Send message to server
  function send(type: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!socket.value?.connected) {
        reject(new Error('WebSocket not connected'))
        return
      }
      
      socket.value.emit(type, data, (response: any) => {
        if (response?.error) {
          reject(new Error(response.error))
        } else {
          resolve(response)
        }
      })
    })
  }
  
  // Register message handler
  function onMessage(type: string, handler: (data: any) => void) {
    if (!messageHandlers.has(type)) {
      messageHandlers.set(type, [])
    }
    messageHandlers.get(type)!.push(handler)
    
    // Return unsubscribe function
    return () => {
      const handlers = messageHandlers.get(type)
      if (handlers) {
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      }
    }
  }
  
  // Remove message handler
  function offMessage(type: string, handler?: (data: any) => void) {
    if (!handler) {
      messageHandlers.delete(type)
    } else {
      const handlers = messageHandlers.get(type)
      if (handlers) {
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      }
    }
  }
  
  // Page-specific methods
  function joinPage(pageId: number, userId?: string) {
    return send('join_page', { page_id: pageId, user_id: userId })
  }
  
  function leavePage(pageId: number, userId?: string) {
    return send('leave_page', { page_id: pageId, user_id: userId })
  }
  
  function updatePage(pageId: number, changes: any, userId?: string) {
    return send('update_page', {
      page_id: pageId,
      changes,
      user_id: userId
    })
  }
  
  function sendCursorPosition(pageId: number, position: number, userId?: string) {
    return send('cursor_position', {
      page_id: pageId,
      position,
      user_id: userId
    })
  }
  
  function executeCode(pageId: number, blockId: number, code: string, language: string) {
    return send('execute_code', {
      page_id: pageId,
      block_id: blockId,
      code,
      language
    })
  }
  
  // Auto-connect on mount
  onMounted(() => {
    connect()
  })
  
  // Cleanup on unmount
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    // State
    connected: readonly(connected),
    error: readonly(error),
    lastMessage: readonly(lastMessage),
    
    // Methods
    connect,
    disconnect,
    send,
    onMessage,
    offMessage,
    
    // Page-specific methods
    joinPage,
    leavePage,
    updatePage,
    sendCursorPosition,
    executeCode
  }
}

// Composable for collaborative editing
export function useCollaborativeEditing(pageId: number) {
  const { connected, onMessage, offMessage, joinPage, leavePage, updatePage, sendCursorPosition } = useWebSocket()
  
  const users = ref<Array<{
    id: string
    name: string
    cursor_position?: number
    last_seen: string
  }>>([])
  
  const pendingChanges = ref<any>({})
  const isReceivingUpdate = ref(false)
  
  // Track user presence
  const unsubscribePresence = onMessage('user_presence', (data: UserPresenceMessage['data']) => {
    if (data.page_id === pageId) {
      users.value = data.users
    }
  })
  
  // Handle page updates from other users
  const unsubscribeUpdates = onMessage('page_updated', (data: PageUpdateMessage['data']) => {
    if (data.page_id === pageId && data.user_id !== getCurrentUserId()) {
      isReceivingUpdate.value = true
      
      // Merge changes
      Object.assign(pendingChanges.value, data.changes)
      
      // Debounce the update application
      setTimeout(() => {
        isReceivingUpdate.value = false
      }, 100)
    }
  })
  
  // Join page collaboration
  async function join(userId?: string) {
    try {
      await joinPage(pageId, userId || getCurrentUserId())
    } catch (error) {
      console.error('Failed to join page collaboration:', error)
    }
  }
  
  // Leave page collaboration
  async function leave(userId?: string) {
    try {
      await leavePage(pageId, userId || getCurrentUserId())
    } catch (error) {
      console.error('Failed to leave page collaboration:', error)
    }
  }
  
  // Send page update to other users
  async function broadcastUpdate(changes: any) {
    if (isReceivingUpdate.value) return // Don't broadcast while receiving updates
    
    try {
      await updatePage(pageId, changes, getCurrentUserId())
    } catch (error) {
      console.error('Failed to broadcast page update:', error)
    }
  }
  
  // Send cursor position to other users
  async function broadcastCursor(position: number) {
    try {
      await sendCursorPosition(pageId, position, getCurrentUserId())
    } catch (error) {
      console.error('Failed to broadcast cursor position:', error)
    }
  }
  
  // Get current user ID (this should be implemented based on your auth system)
  function getCurrentUserId(): string {
    // For now, generate a temporary ID
    return localStorage.getItem('temp_user_id') || 
           (() => {
             const id = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
             localStorage.setItem('temp_user_id', id)
             return id
           })()
  }
  
  // Cleanup
  onUnmounted(() => {
    leave()
    unsubscribePresence()
    unsubscribeUpdates()
  })
  
  return {
    // State
    connected: readonly(connected),
    users: readonly(users),
    pendingChanges: readonly(pendingChanges),
    isReceivingUpdate: readonly(isReceivingUpdate),
    
    // Methods
    join,
    leave,
    broadcastUpdate,
    broadcastCursor
  }
}

// Composable for code execution with real-time results
export function useCodeExecution(pageId: number) {
  const { onMessage, offMessage, executeCode } = useWebSocket()
  
  const executionResults = ref<Map<number, any>>(new Map())
  const executingBlocks = ref<Set<number>>(new Set())
  
  // Handle code execution results
  const unsubscribeExecution = onMessage('code_execution', (data: CodeExecutionMessage['data']) => {
    if (data.page_id === pageId) {
      executionResults.value.set(data.block_id, data.result)
      executingBlocks.value.delete(data.block_id)
    }
  })
  
  // Execute code block
  async function execute(blockId: number, code: string, language: string): Promise<any> {
    executingBlocks.value.add(blockId)
    
    try {
      const result = await executeCode(pageId, blockId, code, language)
      return result
    } catch (error) {
      executingBlocks.value.delete(blockId)
      throw error
    }
  }
  
  // Check if block is executing
  function isExecuting(blockId: number): boolean {
    return executingBlocks.value.has(blockId)
  }
  
  // Get execution result for block
  function getResult(blockId: number): any {
    return executionResults.value.get(blockId)
  }
  
  // Clear result for block
  function clearResult(blockId: number) {
    executionResults.value.delete(blockId)
  }
  
  // Cleanup
  onUnmounted(() => {
    unsubscribeExecution()
  })
  
  return {
    // State
    executionResults: readonly(executionResults),
    executingBlocks: readonly(executingBlocks),
    
    // Methods
    execute,
    isExecuting,
    getResult,
    clearResult
  }
}
