<template>
  <div class="page-view">
    <div v-if="pageStore.loading" class="loading-container">
      <div class="loading-spinner"></div>
      <span>Carregando página...</span>
    </div>
    
    <div v-else-if="pageStore.error" class="error-container">
      <div class="error-content">
        <h2>Erro ao carregar página</h2>
        <p>{{ pageStore.error }}</p>
        <button @click="loadPage" class="retry-btn">Tentar novamente</button>
      </div>
    </div>
    
    <div v-else-if="currentPage" class="page-container">
      <!-- Page Header -->
      <header class="page-header">
        <!-- Breadcrumbs -->
        <nav class="breadcrumbs" v-if="breadcrumbs.length > 1">
          <router-link 
            v-for="(crumb, index) in breadcrumbs" 
            :key="crumb.id"
            :to="index === breadcrumbs.length - 1 ? '' : `/pages/${crumb.id}`"
            class="breadcrumb"
            :class="{ current: index === breadcrumbs.length - 1 }"
          >
            {{ crumb.title }}
          </router-link>
        </nav>
        
        <!-- Page Title -->
        <div class="page-title-section">
          <input 
            v-model="editableTitle"
            @blur="updateTitle"
            @keyup.enter="updateTitle"
            class="page-title-input"
            placeholder="Título da página"
          />
          
          <!-- Page Actions -->
          <div class="page-actions">
            <button 
              class="action-btn"
              @click="sharePageToggle"
              title="Compartilhar"
            >
              <ShareIcon class="action-icon" />
            </button>
            
            <button 
              class="action-btn"
              @click="togglePageMenu"
              title="Mais opções"
            >
              <MoreVerticalIcon class="action-icon" />
            </button>
          </div>
        </div>
        
        <!-- Page Menu -->
        <div v-if="showPageMenu" class="page-menu" @click.stop>
          <button class="menu-item" @click="duplicatePage">
            <CopyIcon class="menu-icon" />
            Duplicar página
          </button>
          <button class="menu-item" @click="exportPage">
            <DownloadIcon class="menu-icon" />
            Exportar
          </button>
          <div class="menu-separator"></div>
          <button class="menu-item danger" @click="deletePage">
            <TrashIcon class="menu-icon" />
            Excluir página
          </button>
        </div>
      </header>
      
      <!-- Share Modal -->
      <BaseModal 
        v-model="showShareModal" 
        size="large"
        title-id="share-modal-title"
      >
        <ShareModal 
          :page="currentPage" 
          @close="closeShareModal"
        />
      </BaseModal>
      
      <!-- Page Content Editor -->
      <main class="page-main">
        <div class="editor-container">
          <SimpleEditor 
            :content="currentPage.content"
            @update="updatePageContent"
            @save="savePage"
          />
        </div>
      </main>
    </div>
    
    <div v-else class="not-found">
      <h2>Página não encontrada</h2>
      <p>A página solicitada não existe ou foi removida.</p>
      <router-link to="/" class="home-link">Voltar ao início</router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePageStore } from '@/stores/pages'
import type { Page } from '@/stores/pages'
import SimpleEditor from '@/components/editor/SimpleEditor.vue'
import ShareModal from '@/components/modals/ShareModal.vue'
import BaseModal from '@/components/modals/BaseModal.vue'
import {
  Share as ShareIcon,
  MoreVertical as MoreVerticalIcon,
  Copy as CopyIcon,
  Download as DownloadIcon,
  Trash2 as TrashIcon
} from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()
const pageStore = usePageStore()

// Local state
const editableTitle = ref('')
const showPageMenu = ref(false)
const showShareModal = ref(false)
const saveTimeout = ref<NodeJS.Timeout | null>(null)

// Computed
const currentPage = computed(() => pageStore.currentPage)

const breadcrumbs = computed(() => {
  if (!currentPage.value) return []
  return pageStore.getPagePath(currentPage.value.id)
})

// Methods
async function loadPage() {
  const pageId = parseInt(route.params.id as string)
  if (pageId) {
    await pageStore.loadPage(pageId)
    if (currentPage.value) {
      editableTitle.value = currentPage.value.title
    }
  }
}

async function updateTitle() {
  if (!currentPage.value || editableTitle.value === currentPage.value.title) return
  
  try {
    await pageStore.updatePage(currentPage.value.id, {
      title: editableTitle.value
    })
  } catch (error) {
    console.error('Error updating title:', error)
    // Revert title on error
    editableTitle.value = currentPage.value.title
  }
}

function updatePageContent(content: string) {
  if (!currentPage.value) return
  
  // Clear existing timeout
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value)
  }
  
  // Auto-save after 2 seconds of inactivity
  saveTimeout.value = setTimeout(() => {
    savePage(content)
  }, 2000)
}

async function savePage(content?: string) {
  if (!currentPage.value) return
  
  const contentToSave = content || currentPage.value.content
  
  try {
    await pageStore.updatePage(currentPage.value.id, {
      content: contentToSave
    })
  } catch (error) {
    console.error('Error saving page:', error)
  }
}

function sharePageToggle() {
  showShareModal.value = true
}

function closeShareModal() {
  showShareModal.value = false
}

function togglePageMenu() {
  showPageMenu.value = !showPageMenu.value
}

async function duplicatePage() {
  if (!currentPage.value) return
  
  try {
    const duplicatedPage = await pageStore.createPage({
      title: `${currentPage.value.title} (Cópia)`,
      content: currentPage.value.content,
      parent_id: currentPage.value.parent_id
    })
    
    router.push(`/pages/${duplicatedPage.id}`)
  } catch (error) {
    console.error('Error duplicating page:', error)
  }
  
  showPageMenu.value = false
}

function exportPage() {
  // TODO: Implement page export
  alert('Funcionalidade de exportação em desenvolvimento')
  showPageMenu.value = false
}

async function deletePage() {
  if (!currentPage.value) return
  
  if (confirm(`Tem certeza que deseja excluir "${currentPage.value.title}"?`)) {
    try {
      await pageStore.deletePage(currentPage.value.id)
      router.push('/')
    } catch (error) {
      console.error('Error deleting page:', error)
    }
  }
  
  showPageMenu.value = false
}

// Click outside handler
function handleClickOutside(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.page-menu') && !target.closest('.action-btn')) {
    showPageMenu.value = false
  }
}

// Watchers
watch(() => route.params.id, () => {
  loadPage()
})

watch(currentPage, (newPage) => {
  if (newPage) {
    editableTitle.value = newPage.title
  }
})

// Lifecycle
onMounted(() => {
  loadPage()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value)
  }
})
</script>

<style scoped>
.page-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

/* Loading and Error States */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: #787774;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9e9e7;
  border-top: 3px solid #2383e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-content h2 {
  color: #e03e3e;
  margin: 0 0 8px 0;
}

.retry-btn {
  background: #2383e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background: #1a6cc7;
}

/* Page Header */
.page-header {
  position: sticky;
  top: 0;
  background: white;
  border-bottom: 1px solid #e9e9e7;
  padding: 16px 24px;
  z-index: 10;
}

/* Breadcrumbs */
.breadcrumbs {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
}

.breadcrumb {
  color: #787774;
  text-decoration: none;
  padding: 4px 6px;
  border-radius: 4px;
  transition: background-color 0.15s ease;
}

.breadcrumb:hover:not(.current) {
  background-color: rgba(55, 53, 47, 0.08);
  color: #37352f;
}

.breadcrumb.current {
  color: #37352f;
  font-weight: 500;
}

.breadcrumb:not(:last-child)::after {
  content: '/';
  margin-left: 8px;
  color: #c9c7c4;
}

/* Page Title */
.page-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title-input {
  flex: 1;
  font-size: 2rem;
  font-weight: 700;
  color: #37352f;
  border: none;
  outline: none;
  background: none;
  padding: 8px 0;
  border-radius: 4px;
  transition: background-color 0.15s ease;
}

.page-title-input:hover, .page-title-input:focus {
  background-color: rgba(55, 53, 47, 0.04);
  padding-left: 8px;
  padding-right: 8px;
}

.page-title-input::placeholder {
  color: #c9c7c4;
}

/* Page Actions */
.page-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

.action-icon {
  width: 18px;
  height: 18px;
  color: #787774;
}

/* Page Menu */
.page-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  min-width: 200px;
  padding: 8px 0;
  margin-top: 4px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 16px;
  border: none;
  background: none;
  text-align: left;
  font-size: 14px;
  color: #37352f;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.menu-item:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

.menu-item.danger {
  color: #e03e3e;
}

.menu-item.danger:hover {
  background-color: rgba(224, 62, 62, 0.1);
}

.menu-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.menu-separator {
  height: 1px;
  background-color: #e9e9e7;
  margin: 8px 0;
}

/* Page Main Content */
.page-main {
  flex: 1;
  overflow-y: auto;
}

.editor-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
}

/* Not Found */
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #787774;
}

.not-found h2 {
  color: #37352f;
  margin: 0 0 8px 0;
}

.home-link {
  color: #2383e2;
  text-decoration: none;
  font-weight: 500;
  margin-top: 16px;
  padding: 8px 16px;
  border: 1px solid #2383e2;
  border-radius: 6px;
  transition: all 0.15s ease;
}

.home-link:hover {
  background: #2383e2;
  color: white;
}

/* Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }
  
  .page-title-input {
    font-size: 1.5rem;
  }
  
  .editor-container {
    padding: 16px;
  }
}
