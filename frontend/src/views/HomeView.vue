<template>
  <div class="home-view">
    <div class="home-header">
      <div class="header-content">
        <h1>Bem-vindo ao Alt-Notion</h1>
        <p class="subtitle">Suas páginas e anotações organizadas de forma inteligente</p>
      </div>
    </div>
    
    <div class="home-content">
      <!-- Quick Actions -->
      <section class="quick-actions">
        <h2>Ações Rápidas</h2>
        <div class="actions-grid">
          <button class="action-card" @click="createNewPage">
            <div class="action-icon">📝</div>
            <div class="action-text">
              <h3>Nova Página</h3>
              <p>Crie uma nova página em branco</p>
            </div>
          </button>
          
          <button class="action-card" @click="importContent">
            <div class="action-icon">📄</div>
            <div class="action-text">
              <h3>Importar</h3>
              <p>Importe conteúdo de outros formatos</p>
            </div>
          </button>
          
          <button class="action-card" @click="exploreTemplates">
            <div class="action-icon">🎨</div>
            <div class="action-text">
              <h3>Templates</h3>
              <p>Comece com um template pré-definido</p>
            </div>
          </button>
        </div>
      </section>
      
      <!-- Recent Pages -->
      <section v-if="recentPages.length > 0" class="recent-pages">
        <h2>Páginas Recentes</h2>
        <div class="pages-list">
          <div 
            v-for="page in recentPages" 
            :key="page.id"
            class="page-card"
            @click="openPage(page)"
          >
            <div class="page-icon">📄</div>
            <div class="page-info">
              <h3>{{ page.title || 'Sem título' }}</h3>
              <p>{{ formatDate(page.updated_at) }}</p>
            </div>
          </div>
        </div>
      </section>
      
      <!-- Getting Started -->
      <section v-if="pageStore.pageTree.length === 0" class="getting-started">
        <h2>Primeiros Passos</h2>
        <div class="steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>Crie sua primeira página</h3>
              <p>Clique em "Nova Página" na sidebar ou use o botão acima</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>Adicione conteúdo</h3>
              <p>Use o editor para escrever texto, adicionar códigos executáveis e mais</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>Organize em hierarquia</h3>
              <p>Crie subpáginas para organizar seu conteúdo</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>Compartilhe</h3>
              <p>Gere links para compartilhar suas páginas com outros</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { usePageStore } from '@/stores/pages'

const router = useRouter()
const pageStore = usePageStore()

// Computed
const recentPages = computed(() => {
  // Get all pages, sort by updated_at, take first 6
  return [...pageStore.pages]
    .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    .slice(0, 6)
})

// Methods
async function createNewPage() {
  try {
    const newPage = await pageStore.createPage({
      title: 'Nova Página',
      content: '',
      parent_id: null
    })
    
    router.push(`/pages/${newPage.id}`)
  } catch (error) {
    console.error('Error creating page:', error)
  }
}

function openPage(page: any) {
  router.push(`/pages/${page.id}`)
}

function importContent() {
  // TODO: Implement import functionality
  alert('Funcionalidade de importação em desenvolvimento')
}

function exploreTemplates() {
  // TODO: Implement templates
  alert('Templates em desenvolvimento')
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return 'Hoje'
  } else if (days === 1) {
    return 'Ontem'
  } else if (days < 7) {
    return `${days} dias atrás`
  } else {
    return date.toLocaleDateString('pt-BR')
  }
}
</script>

<style scoped>
.home-view {
  height: 100%;
  overflow-y: auto;
}

.home-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 48px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

.home-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 24px;
}

section {
  margin-bottom: 64px;
}

section h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #37352f;
}

/* Quick Actions */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.action-card:hover {
  border-color: #2383e2;
  box-shadow: 0 4px 20px rgba(35, 131, 226, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f6f3;
  border-radius: 12px;
  flex-shrink: 0;
}

.action-text h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #37352f;
}

.action-text p {
  font-size: 0.9rem;
  color: #787774;
  margin: 0;
  line-height: 1.4;
}

/* Recent Pages */
.pages-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.page-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.page-card:hover {
  border-color: #2383e2;
  box-shadow: 0 2px 10px rgba(35, 131, 226, 0.1);
}

.page-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f6f3;
  border-radius: 6px;
  flex-shrink: 0;
}

.page-info h3 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #37352f;
}

.page-info p {
  font-size: 0.85rem;
  color: #787774;
  margin: 0;
}

/* Getting Started */
.steps {
  display: grid;
  gap: 24px;
  margin-top: 32px;
}

.step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #2383e2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #37352f;
}

.step-content p {
  font-size: 0.95rem;
  color: #787774;
  margin: 0;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .header-content h1 {
    font-size: 2rem;
  }
  
  .home-content {
    padding: 32px 16px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .action-card {
    padding: 20px;
  }
  
  .pages-list {
    grid-template-columns: 1fr;
  }
}
</style>
