<template>
  <div class="editor-view">
    <!-- Navigation Bar -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="nav-left">
          <div class="brand">
            <button @click="toggleSidebar" class="nav-btn" title="Alternar sidebar">
              <MenuIcon class="btn-icon" />
            </button>
            <span class="brand-name">Alt Notion</span>
          </div>
        </div>
        
        <div class="nav-center">
          <div class="breadcrumb">
            <span class="breadcrumb-item">Workspace</span>
            <ChevronRightIcon class="breadcrumb-separator" />
            <span class="breadcrumb-item breadcrumb-item--current">{{ document.title || 'Documento sem título' }}</span>
          </div>
        </div>
        
        <div class="nav-right">
           <button @click="saveDocument" class="btn btn-primary">
              💾 Salvar
            </button>
            <span v-if="lastSaved" class="save-status">
              Salvo há {{ formatTime(lastSaved) }}
            </span>
          <button @click="shareDocument" class="nav-btn" title="Compartilhar documento">
            <ShareIcon class="btn-icon" />
          </button>
          
          <div class="user-menu">
            <button class="user-avatar" title="Menu do usuário">
              <UserIcon class="user-icon" />
            </button>
          </div>
        </div>
      </div>
    </nav>
    
    <!-- Main Layout -->
    <div class="main-layout" :class="{ 'main-layout--sidebar-open': sidebarOpen }">
      <!-- Sidebar -->
      <aside class="sidebar" :class="{ 'sidebar--open': sidebarOpen }">
        <div class="sidebar-content">
          <div class="sidebar-section">
            <h3 class="sidebar-title">Documentos Recentes</h3>
            <ul class="document-list">
              <li
                v-for="doc in recentDocuments"
                :key="doc.id"
                class="document-item"
                :class="{ 'document-item--active': doc.id === document.id }"
                @click="loadDocument(doc.id)"
              >
                <FileTextIcon class="doc-icon" />
                <span class="doc-title">{{ doc.title }}</span>
                <span class="doc-date">{{ formatDate(doc.updatedAt) }}</span>
              </li>
            </ul>
          </div>
          
          <div class="sidebar-section">
            <div class="sidebar-header">
              <h3 class="sidebar-title">Templates</h3>
              <button @click="showTemplates = !showTemplates" class="toggle-btn">
                <ChevronDownIcon 
                  class="toggle-icon" 
                  :class="{ 'toggle-icon--rotated': !showTemplates }" 
                />
              </button>
            </div>
            
            <ul v-if="showTemplates" class="template-list">
              <li
                v-for="template in templates"
                :key="template.id"
                class="template-item"
                @click="createFromTemplate(template)"
              >
                <component :is="template.icon" class="template-icon" />
                <div class="template-info">
                  <span class="template-name">{{ template.name }}</span>
                  <span class="template-desc">{{ template.description }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      
      <!-- Main Content -->
      <main class="main-content">
        <div class="editor-content">
      <!-- Empty State -->
      <div v-if="blocks.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <h2>Comece a escrever</h2>
        <p>Clique no botão abaixo para adicionar seu primeiro bloco</p>
        <button @click="addFirstBlock" class="btn btn-primary btn-large">
          ➕ Adicionar primeiro bloco
        </button>
      </div>

      <!-- Blocks List -->
      <div v-else class="blocks-list">
        <div
          v-for="(block, index) in blocks"
          :key="block.id"
          class="block-wrapper"
          :class="{ 'block-focused': focusedBlockId === block.id }"
        >
          <!-- Text Block -->
          <div v-if="block.type === 'text'" class="text-block">
            <div
              ref="textElements"
              :data-block-id="block.id"
              contenteditable="true"
              @input="updateTextBlock($event, block.id)"
              @focus="focusBlock(block.id)"
              @keydown="handleKeydown($event, block.id, index)"
              class="text-input"
              :class="`text-${block.subtype}`"
              :data-placeholder="getPlaceholder(block.subtype)"
            ></div>
          </div>

<!-- Code Block -->
          <PythonBlock 
            v-else-if="block.type === 'code'"
            :block="block"
            @update:content="block.content = $event"
          />
          <div v-if="focusedBlockId === block.id" class="block-actions">
          <button @click="cycleBlockType(block.id)" title="Mudar tipo">
              🔄
            </button>
            <button @click="duplicateBlock(index)" title="Duplicar">
              📋
            </button>
            <button @click="deleteBlock(index)" title="Deletar">
              🗑️
            </button>
          </div>
        </div>

        <!-- Add Block Button -->
        <div class="add-block-wrapper">
          <button @click="addNewBlock" class="btn btn-secondary">
            ➕ Adicionar bloco
          </button>
        </div>
      </div>
    </div>
      </main>
    </div>
    
<!-- Toast Notifications -->
    <div class="toast-container">
      <div
        v-for="toast in toasts"
        :key="toast.id"
        class="toast"
        :class="`toast--${toast.type}`"
      >
        <component :is="getToastIcon(toast.type)" class="toast-icon" />
        <span class="toast-message">{{ toast.message }}</span>
        <button
          @click="removeToast(toast.id)"
          class="toast-close"
        >
          <XIcon class="close-icon" />
        </button>
      </div>
    </div>
    <div
      v-if="showCommandMenu"
      class="command-menu"
      :style="{ top: `${commandMenuPosition.top}px`, left: `${commandMenuPosition.left}px` }"
    >
      <div class="command-menu-content">
        <div class="command-menu-title">Blocos Básicos</div>
        <div
          v-for="(command, index) in filteredCommands"
          :key="command.id"
          class="command-item"
          :class="{ 'command-item--active': index === activeCommandIndex }"
          @click="selectCommand(command)"
        >
          <div class="command-item-icon">
            <component :is="command.icon" size="24" />
          </div>
          <div class="command-item-text">
            <div class="command-item-name">{{ command.name }}</div>
            <div class="command-item-desc">{{ command.description }}</div>
          </div>
        </div>
        <div v-if="filteredCommands.length === 0" class="command-empty">
          Nenhum resultado
        </div>
      </div>
    </div>
    <div
      v-if="showShareModal"
      class="modal-overlay"
      @click="showShareModal = false"
    >
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">Compartilhar Documento</h2>
          <button
            @click="showShareModal = false"
            class="modal-close"
          >
            <XIcon class="close-icon" />
          </button>
        </div>
        
        <div class="modal-content">
          <div class="share-option">
            <h3>Link de Compartilhamento</h3>
            <div class="share-link-container">
              <input
                ref="shareLinkInput"
                :value="shareLink"
                readonly
                class="share-link-input"
              >
              <button
                @click="copyShareLink"
                class="copy-btn"
                :class="{ 'copy-btn--copied': linkCopied }"
              >
                <CheckIcon v-if="linkCopied" class="btn-icon" />
                <CopyIcon v-else class="btn-icon" />
                {{ linkCopied ? 'Copiado!' : 'Copiar' }}
              </button>
            </div>
          </div>
          
        </div>
        
        <div class="modal-footer">
          <button
            @click="showShareModal = false"
            class="modal-btn modal-btn--secondary"
          >
            Cancelar
          </button>
          <button
            @click="generateShareLink"
            class="modal-btn modal-btn--primary"
          >
            Gerar Link
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  FileText as FileTextIcon,
  ChevronRight as ChevronRightIcon,
  Menu as MenuIcon,
  Share as ShareIcon,
  User as UserIcon,
  X as XIcon,
  Copy as CopyIcon,
  Check as CheckIcon,
  Type as TypeIcon,
  Heading1 as Heading1Icon,
  Heading2 as Heading2Icon,
  Quote as QuoteIcon,
  ChevronDown as ChevronDownIcon,
  BookOpen as BookOpenIcon,
  Briefcase as BriefcaseIcon,
  Calendar as CalendarIcon,
  Code as CodeIcon,
  CheckCircle as CheckCircleIcon,
  AlertCircle as AlertCircleIcon,
  Info as InfoIcon
} from 'lucide-vue-next'
import { v4 as uuidv4 } from 'uuid'
import PythonBlock from '../components/PythonBlock.vue';

interface Block {
  id: string;
  type: 'text' | 'image' | 'code';
  subtype?: 'paragraph' | 'heading1' | 'heading2' | 'quote';
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Document {
  id: string;
  title: string;
  blocks: Block[];
  createdAt: Date;
  updatedAt: Date;
}

interface Toast {
  id: string;
  type: 'success' | 'error' | 'info';
  message: string;
}

const router = useRouter()
const route = useRoute()

// State
const sidebarOpen = ref(false)
const showShareModal = ref(false)
const linkCopied = ref(false)
const shareLink = ref('')
const shareLinkInput = ref<HTMLInputElement | null>(null)
const lastSaved = ref<Date | null>(null)
const showTemplates = ref(true);
const toasts = ref<Toast[]>([]);


const document = ref<Document>({
  id: uuidv4(),
  title: '',
  blocks: [],
  createdAt: new Date(),
  updatedAt: new Date()
})
const blocks = ref<Block[]>([]);
const focusedBlockId = ref<string | null>(null);

// Command Menu State
const showCommandMenu = ref(false);
const commandMenuPosition = ref({ top: 0, left: 0 });
const commandQuery = ref('');
const activeCommandIndex = ref(0);
const commands = ref([
  { id: 'paragraph', name: 'Texto', description: 'Parágrafo de texto simples', icon: TypeIcon, action: () => changeBlockType(focusedBlockId.value, 'paragraph') },
  { id: 'heading1', name: 'Título 1', description: 'Título de seção grande', icon: Heading1Icon, action: () => changeBlockType(focusedBlockId.value, 'heading1') },
  { id: 'heading2', name: 'Título 2', description: 'Título de seção médio', icon: Heading2Icon, action: () => changeBlockType(focusedBlockId.value, 'heading2') },
  { id: 'quote', name: 'Citação', description: 'Texto de citação destacado', icon: QuoteIcon, action: () => changeBlockType(focusedBlockId.value, 'quote') },
  { id: 'code', name: 'Código', description: 'Bloco para código Python', icon: CodeIcon, action: () => changeBlockType(focusedBlockId.value, 'code', 'python') },
]);

const filteredCommands = computed(() => {
  if (!commandQuery.value) {
    return commands.value;
  }
  return commands.value.filter(command =>
    command.name.toLowerCase().includes(commandQuery.value.toLowerCase())
  );
});

interface RecentDocument {
  id: string;
  title: string;
  updatedAt: Date;
}

interface Template {
  id: string;
  name: string;
  description: string;
  icon: any;
  blocks: Block[];
}

const recentDocuments = ref<RecentDocument[]>([
  {
    id: '1',
    title: 'Relatório de Vendas Q4',
    updatedAt: new Date(Date.now() - 1000 * 60 * 30) // 30 min ago
  },
  {
    id: '2',
    title: 'Planejamento de Projeto',
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
  },
  {
    id: '3',
    title: 'Meeting Notes - 27/08',
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1 day ago
  },
]);

const templates = ref<Template[]>([
  {
    id: 'blank',
    name: 'Documento em Branco',
    description: 'Comece do zero',
    icon: FileTextIcon,
    blocks: []
  },
  {
    id: 'meeting-notes',
    name: 'Notas de Reunião',
    description: 'Template para atas de reunião',
    icon: CalendarIcon,
    blocks: [
      { id: uuidv4(), type: 'text', subtype: 'heading1', content: 'Notas da Reunião', createdAt: new Date(), updatedAt: new Date() },
      { id: uuidv4(), type: 'text', subtype: 'paragraph', content: 'Data:', createdAt: new Date(), updatedAt: new Date() },
    ]
  },
  {
    id: 'project-plan',
    name: 'Plano de Projeto',
    description: 'Template para planejamento',
    icon: BriefcaseIcon,
    blocks: [
        { id: uuidv4(), type: 'text', subtype: 'heading1', content: 'Plano de Projeto', createdAt: new Date(), updatedAt: new Date() },
    ]
  },
]);

// Methods from WorkingEditor
function generateId() {
  return `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

function createTextBlock(subtype: string = 'paragraph', content: string = ''): Block {
  return {
    id: generateId(),
    type: subtype === 'code' ? 'code' : 'text',
    subtype: subtype as any,
    content,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

function addFirstBlock() {
  const newBlock = createTextBlock()
  blocks.value.push(newBlock)
  
  nextTick(() => {
    focusBlock(newBlock.id)
    focusTextElement(newBlock.id)
  })
}

function addNewBlock() {
  const newBlock = createTextBlock()
  blocks.value.push(newBlock)
  
  nextTick(() => {
    focusBlock(newBlock.id)
    focusTextElement(newBlock.id)
  })
}

function updateTextBlock(event: Event, blockId: string) {
  const target = event.target as HTMLElement
  const block = blocks.value.find(b => b.id === blockId)
  if (block) {
    block.content = target.textContent || ''
    block.updatedAt = new Date()
    document.value.blocks = blocks.value;

    const content = target.textContent || '';
    if (content.includes('/') && focusedBlockId.value === blockId) {
      const match = /\/(\w*)$/.exec(content);
      if (match) {
        commandQuery.value = match[1];
        openCommandMenu(target);
      } else {
        closeCommandMenu();
      }
    } else {
      closeCommandMenu();
    }
  }
}

function focusBlock(blockId: string) {
  focusedBlockId.value = blockId
}

function focusTextElement(blockId: string) {
  setTimeout(() => {
    const element = document.querySelector(`[data-block-id="${blockId}"]`) as HTMLElement
    if (element) {
      element.focus()
      const range = document.createRange()
      const sel = window.getSelection()
      range.selectNodeContents(element)
      range.collapse(false)
      sel?.removeAllRanges()
      sel?.addRange(range)
    }
  }, 100)
}

function handleKeydown(event: KeyboardEvent, blockId: string, index: number) {
  if (event.key === 'Enter' && !event.shiftKey) {
    // Se o menu de comandos estiver aberto, não criar novo bloco
    if (showCommandMenu.value) {
      return;
    }
    
    event.preventDefault()
    
    const newBlock = createTextBlock()
    blocks.value.splice(index + 1, 0, newBlock)
    
    nextTick(() => {
      focusBlock(newBlock.id)
      focusTextElement(newBlock.id)
    })
  } else if (event.key === 'Backspace') {
    const target = event.target as HTMLElement
    if (target.textContent === '' && blocks.value.length > 1) {
      event.preventDefault()
      deleteBlock(index)
      
      if (index > 0) {
        const prevBlock = blocks.value[index - 1]
        nextTick(() => {
          focusBlock(prevBlock.id)
          focusTextElement(prevBlock.id)
        })
      }
    }
  }

  if (showCommandMenu.value) {
    if (event.key === 'ArrowUp') {
      event.preventDefault();
      activeCommandIndex.value = (activeCommandIndex.value - 1 + filteredCommands.value.length) % filteredCommands.value.length;
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      activeCommandIndex.value = (activeCommandIndex.value + 1) % filteredCommands.value.length;
    } else if (event.key === 'Enter') {
      event.preventDefault();
      const command = filteredCommands.value[activeCommandIndex.value];
      if (command) {
        selectCommand(command);
      }
    } else if (event.key === 'Escape') {
      event.preventDefault();
      closeCommandMenu();
    }
  }
}

function deleteBlock(index: number) {
  blocks.value.splice(index, 1)
}

function duplicateBlock(index: number) {
  const block = blocks.value[index]
  const newBlock = createTextBlock(block.subtype, block.content)
  blocks.value.splice(index + 1, 0, newBlock)
}

function cycleBlockType(blockId: string) {
  const block = blocks.value.find(b => b.id === blockId);
  if (block) {
    const types = ['paragraph', 'heading1', 'heading2', 'quote'];
    const currentIndex = types.indexOf(block.subtype || 'paragraph');
    const nextIndex = (currentIndex + 1) % types.length;
    block.subtype = types[nextIndex] as any;
  }
}

function changeBlockType(blockId: string, type: string, language?: string) {
  const block = blocks.value.find(b => b.id === blockId);
  if (block) {
    if (type === 'code') {
      block.type = 'code';
      block.subtype = language || 'python'; 
    } else {
      block.type = 'text';
      block.subtype = type as any;
    }
    
    const element = document.querySelector(`[data-block-id="${blockId}"]`) as HTMLElement;
    if (element) {
      element.textContent = element.textContent?.replace(/\/\w*$/, '') || '';
      element.dispatchEvent(new Event('input', { bubbles: true }));
    }
    nextTick(() => focusTextElement(blockId));
  }
}

function saveDocument() {
  console.log('Saving document...', document.value);
  lastSaved.value = new Date();

  // Create downloadable JSON
  const dataStr = JSON.stringify(document.value, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${document.value.title || 'documento'}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  showToast('success', 'Documento salvo com sucesso!');
}

function formatTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes === 0) return 'agora'
  if (minutes === 1) return '1 minuto'
  if (minutes < 60) return `${minutes} minutos`
  
  const hours = Math.floor(minutes / 60)
  if (hours === 1) return '1 hora'
  return `${hours} horas`
}

function formatDate(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / 60000);
  
  if (diffMinutes === 0) return 'agora';
  if (diffMinutes === 1) return 'há 1 minuto';
  if (diffMinutes < 60) return `há ${diffMinutes} min`;
  
  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours === 1) return 'há 1 hora';
  if (diffHours < 24) return `há ${diffHours}h`;
  
  const diffDays = Math.floor(diffHours / 24);
  if (diffDays === 1) return 'ontem';
  if (diffDays < 7) return `há ${diffDays} dias`;
  
  return date.toLocaleDateString('pt-BR');
}

function getPlaceholder(subtype?: string): string {
  switch (subtype) {
    case 'heading1': return 'Digite um título...'
    case 'heading2': return 'Digite um subtítulo...'
    case 'quote': return 'Digite uma citação...'
    default: return 'Digite algo ou pressione / para comandos...'
  }
}

// Methods from EditorView
function toggleSidebar() {
  sidebarOpen.value = !sidebarOpen.value
}

function shareDocument() {
  showShareModal.value = true
  generateShareLink()
}

function generateShareLink() {
  const baseUrl = window.location.origin
  shareLink.value = `${baseUrl}/doc/${document.value.id}`
}

async function copyShareLink() {
  try {
    await navigator.clipboard.writeText(shareLink.value)
    linkCopied.value = true
    setTimeout(() => {
      linkCopied.value = false
    }, 2000)
  } catch (error) {
    alert('Erro ao copiar link')
  }
}

function loadDocument(documentId: string) {
  const doc = recentDocuments.value.find(d => d.id === documentId);
  if (doc) {
    document.value.id = doc.id;
    document.value.title = doc.title;
    document.value.updatedAt = doc.updatedAt;
    // Here you would fetch blocks from your backend
    blocks.value = [];
    sidebarOpen.value = false;
  }
}

function createFromTemplate(template: Template) {
  document.value.title = template.name;
  blocks.value = template.blocks.map(b => ({...b, id: generateId()}));
  sidebarOpen.value = false;
}

function openCommandMenu(target: HTMLElement) {
  const rect = target.getBoundingClientRect();
  commandMenuPosition.value = {
    top: rect.bottom + window.scrollY + 5,
    left: rect.left + window.scrollX,
  };
  showCommandMenu.value = true;
  activeCommandIndex.value = 0;
}

function closeCommandMenu() {
  showCommandMenu.value = false;
  commandQuery.value = '';
}

function selectCommand(command: any) {
  command.action();
  closeCommandMenu();
}

function showToast(type: Toast['type'], message: string) {
  const toast: Toast = {
    id: uuidv4(),
    type,
    message
  };
  toasts.value.push(toast);
  setTimeout(() => {
    removeToast(toast.id);
  }, 4000);
}

function removeToast(toastId: string) {
  const index = toasts.value.findIndex(t => t.id === toastId);
  if (index >= 0) {
    toasts.value.splice(index, 1);
  }
}

function getToastIcon(type: Toast['type']) {
  switch (type) {
    case 'success': return CheckCircleIcon;
    case 'error': return AlertCircleIcon;
    case 'info': return InfoIcon;
    default: return InfoIcon;
  }
}

// Lifecycle
onMounted(() => {
  // You can add any initialization logic here
})
</script>

<style scoped>
/* GENERAL STYLES */
.editor-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #ffffff;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background: #e9ecef;
}

.btn-large {
  padding: 12px 24px;
  font-size: 16px;
}

/* NAVBAR */
.navbar {
  background: #ffffff;
  border-bottom: 1px solid #e9e9e7;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 48px;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-left {
  display: flex;
  align-items: center;
  min-width: 200px;
}

.brand {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #37352f;
}

.brand-name {
    margin-left: 10px;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #787774;
}

.breadcrumb-separator {
  width: 12px;
  height: 12px;
}

.breadcrumb-item--current {
  color: #37352f;
  font-weight: 500;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  justify-content: flex-end;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.nav-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.btn-icon {
  width: 16px;
  height: 16px;
  color: #787774;
}

.user-avatar {
  width: 28px;
  height: 28px;
  background: #2383e2;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.user-icon {
  width: 16px;
  height: 16px;
  color: white;
}
.save-status {
  color: #666;
  font-size: 14px;
}


/* MAIN LAYOUT */
.main-layout {
  display: flex;
  flex: 1;
  position: relative;
}

/* SIDEBAR */
.sidebar {
  width: 260px;
  background: #f7f6f3;
  border-right: 1px solid #e9e9e7;
  transform: translateX(-260px);
  transition: transform 0.25s ease;
  position: fixed;
  top: 48px;
  left: 0;
  bottom: 0;
  z-index: 50;
  overflow-y: auto;
}

.sidebar--open {
  transform: translateX(0);
}

/* MAIN CONTENT */
.main-content {
  flex: 1;
  transition: margin-left 0.25s ease;
  background: #ffffff;
  overflow-x: hidden;
  width: 100%;
  position: relative;
  min-height: calc(100vh - 48px);
  padding: 20px;
}

.main-layout--sidebar-open .main-content {
  margin-left: 260px;
}

.editor-content {
    max-width: 900px;
    margin: 0 auto;
}


/* BLOCKS */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.blocks-list {
  padding: 20px 0;
}

.block-wrapper {
  position: relative;
  margin: 15px 0;
  padding: 0;
  border-radius: 4px;
  transition: background 0.2s;
}

.block-focused {
  background: rgba(0, 123, 255, 0.05);
}

.text-block {
  position: relative;
}

.text-input {
  width: 100%;
  min-height: 30px;
  padding: 8px 40px 8px 12px;
  border: none;
  outline: none;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  background: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-input:empty::before {
  content: attr(data-placeholder);
  color: #aaa;
  pointer-events: none;
}

.text-heading1 {
  font-size: 32px;
  font-weight: 700;
  margin: 20px 0 10px;
}

.text-heading2 {
  font-size: 24px;
  font-weight: 600;
  margin: 15px 0 8px;
}

.text-quote {
  font-style: italic;
  padding-left: 20px;
  border-left: 3px solid #ddd;
  color: #666;
}

.block-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 5px;
  background: white;
  padding: 4px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.block-actions button {
  width: 28px;
  height: 28px;
  border: none;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.block-actions button:hover {
  background: #e9ecef;
  transform: scale(1.1);
}

.add-block-wrapper {
  margin: 20px 0;
  text-align: center;
}

/* SHARE MODAL */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9e9e7;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #37352f;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.modal-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.share-link-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  font-size: 14px;
  background: #f7f6f3;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.copy-btn--copied {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9e9e7;
  justify-content: flex-end;
}

.modal-btn {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.modal-btn--secondary {
  background: white;
  border-color: #e9e9e7;
  color: #37352f;
}

.modal-btn--primary:hover {
  background: #1a6bb8;
}

/* COMMAND MENU */
.command-menu {
  position: absolute;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #e9e9e7;
  width: 300px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.command-menu-title {
  font-size: 12px;
  color: #9b9a97;
  padding: 4px 8px;
  text-transform: uppercase;
}

.command-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.1s ease;
}

.command-item:hover, .command-item--active {
  background: #f7f6f3;
}

.command-item-icon {
  color: #787774;
}

.command-item-name {
  font-size: 14px;
  color: #37352f;
  font-weight: 500;
}

.command-item-desc {
  font-size: 12px;
  color: #9b9a97;
}

.command-empty {
  padding: 16px;
  text-align: center;
  color: #9b9a97;
}

/* SIDEBAR STYLES */
.sidebar-content {
  padding: 20px;
}

.sidebar-section {
  margin-bottom: 32px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: #37352f;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.toggle-icon {
  width: 14px;
  height: 14px;
  color: #787774;
  transition: transform 0.15s ease;
}

.toggle-icon--rotated {
  transform: rotate(-90deg);
}

.document-list,
.template-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.document-item,
.template-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  margin-bottom: 2px;
}

.document-item:hover,
.template-item:hover {
  background: rgba(55, 53, 47, 0.08);
}

.document-item--active {
  background: rgba(35, 131, 226, 0.1);
  color: #2383e2;
}

.doc-icon,
.template-icon {
  width: 16px;
  height: 16px;
  color: #787774;
  flex-shrink: 0;
}

.document-item--active .doc-icon {
  color: #2383e2;
}

.doc-title,
.template-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.doc-date {
  font-size: 12px;
  color: #9b9a97;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-desc {
  display: block;
  font-size: 12px;
  color: #787774;
  margin-top: 2px;
}

/* TOAST NOTIFICATIONS */
.toast-container {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #e9e9e7;
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.toast--success { border-left: 4px solid #10b981; }
.toast--error { border-left: 4px solid #ef4444; }
.toast--info { border-left: 4px solid #2383e2; }

.toast-icon { width: 16px; height: 16px; flex-shrink: 0; }
.toast--success .toast-icon { color: #10b981; }
.toast--error .toast-icon { color: #ef4444; }
.toast--info .toast-icon { color: #2383e2; }

.toast-message { flex: 1; font-size: 14px; color: #37352f; }

.toast-close { background: none; border: none; cursor: pointer; padding: 2px; border-radius: 2px; }
.toast-close:hover { background: rgba(55, 53, 47, 0.08); }
.close-icon { width: 14px; height: 14px; color: #9b9a97; }
</style>

