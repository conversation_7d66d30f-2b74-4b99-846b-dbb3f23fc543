import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Ref } from 'vue'
import axios from 'axios'

// Types
export interface Page {
  id: number
  title: string
  content: string
  parent_id: number | null
  order_index: number
  created_at: string
  updated_at: string
  is_public: boolean
  children?: Page[]
}

export interface Block {
  id: number
  page_id: number
  block_type: string
  content: string
  properties?: string
  code_language?: string
  execution_result?: string
  is_executable: boolean
  order_index: number
  created_at: string
  updated_at: string
}

export interface Share {
  id: number
  page_id: number
  share_token: string
  is_active: boolean
  allow_comments: boolean
  password_protected: boolean
  expires_at: string | null
  max_views: number | null
  current_views: number
  description: string | null
  share_url: string
}

// API client
const API_BASE = 'http://localhost:5001/api'

const apiClient = axios.create({
  baseURL: API_BASE,
  headers: {
    'Content-Type': 'application/json'
  }
})

export const usePageStore = defineStore('pages', () => {
  // State
  const pages: Ref<Page[]> = ref([])
  const currentPage: Ref<Page | null> = ref(null)
  const loading = ref(false)
  const error = ref('')

  // Computed
  const rootPages = computed(() => 
    pages.value.filter(page => !page.parent_id)
  )

  const pageTree = computed(() => {
    const buildTree = (parentId: number | null = null): Page[] => {
      return pages.value
        .filter(page => page.parent_id === parentId)
        .sort((a, b) => a.order_index - b.order_index)
        .map(page => ({
          ...page,
          children: buildTree(page.id)
        }))
    }
    return buildTree()
  })

  // Actions
  async function loadPages() {
    loading.value = true
    error.value = ''
    
    try {
      const response = await apiClient.get('/pages/')
      pages.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Failed to load pages'
      console.error('Error loading pages:', err)
    } finally {
      loading.value = false
    }
  }

  async function loadPage(pageId: number) {
    loading.value = true
    error.value = ''
    
    try {
      const response = await apiClient.get(`/pages/${pageId}`)
      currentPage.value = response.data
      
      // Update or add to pages array
      const existingIndex = pages.value.findIndex(p => p.id === pageId)
      if (existingIndex >= 0) {
        pages.value[existingIndex] = response.data
      } else {
        pages.value.push(response.data)
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Failed to load page'
      console.error('Error loading page:', err)
    } finally {
      loading.value = false
    }
  }

  async function createPage(pageData: Partial<Page>) {
    loading.value = true
    error.value = ''
    
    try {
      const response = await apiClient.post('/pages/', pageData)
      pages.value.push(response.data)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Failed to create page'
      console.error('Error creating page:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updatePage(pageId: number, updates: Partial<Page>) {
    loading.value = true
    error.value = ''
    
    try {
      const response = await apiClient.put(`/pages/${pageId}`, updates)
      
      // Update in pages array
      const index = pages.value.findIndex(p => p.id === pageId)
      if (index >= 0) {
        pages.value[index] = response.data
      }
      
      // Update current page if it's the one being edited
      if (currentPage.value?.id === pageId) {
        currentPage.value = response.data
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Failed to update page'
      console.error('Error updating page:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deletePage(pageId: number) {
    loading.value = true
    error.value = ''
    
    try {
      await apiClient.delete(`/pages/${pageId}`)
      
      // Remove from pages array
      pages.value = pages.value.filter(p => p.id !== pageId)
      
      // Clear current page if it was deleted
      if (currentPage.value?.id === pageId) {
        currentPage.value = null
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Failed to delete page'
      console.error('Error deleting page:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  function findPage(pageId: number): Page | null {
    const findInTree = (pages: Page[]): Page | null => {
      for (const page of pages) {
        if (page.id === pageId) return page
        if (page.children) {
          const found = findInTree(page.children)
          if (found) return found
        }
      }
      return null
    }
    
    return findInTree(pages.value)
  }

  function getPagePath(pageId: number): Page[] {
    const path: Page[] = []
    let page = findPage(pageId)
    
    while (page) {
      path.unshift(page)
      page = page.parent_id ? findPage(page.parent_id) : null
    }
    
    return path
  }

  function clearError() {
    error.value = ''
  }

  function setCurrentPage(page: Page | null) {
    currentPage.value = page
  }

  return {
    // State
    pages,
    currentPage,
    loading,
    error,
    
    // Computed
    rootPages,
    pageTree,
    
    // Actions
    loadPages,
    loadPage,
    createPage,
    updatePage,
    deletePage,
    findPage,
    getPagePath,
    clearError,
    setCurrentPage
  }
})
