<template>
  <div class="python-block">
    <div class="code-editor-wrapper">
      <textarea
        ref="textareaRef"
        class="code-editor"
        placeholder="# Digite seu código Python aqui..."
        :value="block.content"
        @input="updateContent"
        @keydown.tab.prevent="handleTab"
      ></textarea>
    </div>
    <div class="run-bar">
      <button @click="runCode" class="run-btn" :disabled="running">
        <PlayIcon size="14" />
        {{ running ? 'Executando...' : 'Executar' }}
      </button>
      <div v-if="output" class="output-container">
        <div class="output-header">Saída:</div>
        <pre class="output-content">{{ output }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { PlayIcon } from 'lucide-vue-next';

const props = defineProps<{ block: any }>();
const emit = defineEmits(['update:content']);

const output = ref('');
const running = ref(false);
const textareaRef = ref<HTMLTextAreaElement | null>(null);

function updateContent(event: Event) {
  const target = event.target as HTMLTextAreaElement;
  emit('update:content', target.value);
}

async function runCode() {
  running.value = true;
  output.value = '';

  try {
    const response = await fetch('http://localhost:5001/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code: props.block.content }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    output.value = data.output;
  } catch (e: any) {
    output.value = `Erro ao conectar com o backend: ${e.message}`;
  } finally {
    running.value = false;
  }
}

function handleTab(event: KeyboardEvent) {
  const textarea = textareaRef.value;
  if (!textarea) return;

  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;

  // Inserir 2 espaços para o tab
  const newContent = props.block.content.substring(0, start) + '  ' + props.block.content.substring(end);
  emit('update:content', newContent);

  // Manter o cursor na posição correta
  nextTick(() => {
    textarea.selectionStart = textarea.selectionEnd = start + 2;
  });
}

</script>

<style scoped>
.python-block {
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  background: #fafaf9;
  margin: 10px 0;
  overflow: hidden;
}

.code-editor {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: none;
  outline: none;
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  background: transparent;
  resize: vertical;
  color: #37352f;
}

.run-bar {
  padding: 8px 12px;
  background: #f7f6f3;
  border-top: 1px solid #e9e9e7;
}

.run-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background: #2383e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.run-btn:hover {
  background: #1a6bb8;
}

.run-btn:disabled {
  background: #a0c3e2;
  cursor: not-allowed;
}

.output-container {
  margin-top: 10px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9e9e7;
}

.output-header {
  font-size: 12px;
  font-weight: 600;
  color: #787774;
  margin-bottom: 4px;
}

.output-content {
  margin: 0;
  padding: 0;
  font-family: 'Fira Code', monospace;
  font-size: 14px;
  color: #37352f;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>

