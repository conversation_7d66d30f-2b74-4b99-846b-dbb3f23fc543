<template>
  <div class="image-block">
    <!-- Upload State -->
    <div v-if="!block.content.url" class="upload-area">
      <div class="upload-content">
        <div class="upload-icon">🖼️</div>
        <h4>Adicionar Imagem</h4>
        <div class="upload-buttons">
          <button @click="triggerFileUpload" class="upload-btn">
            📎 Upload
          </button>
          <button @click="showUrlInput = true" class="upload-btn url-btn">
            🔗 URL
          </button>
        </div>
        
        <!-- URL Input -->
        <div v-if="showUrlInput" class="url-input-area">
          <input
            v-model="imageUrl"
            @keydown.enter="loadFromUrl"
            placeholder="Cole a URL da imagem..."
            class="url-input"
          />
          <div class="url-actions">
            <button @click="loadFromUrl" class="url-action-btn">✅</button>
            <button @click="cancelUrl" class="url-action-btn cancel">❌</button>
          </div>
        </div>
      </div>
      
      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="handleFileUpload"
        style="display: none"
      />
    </div>
    
    <!-- Image Display -->
    <div v-else class="image-display">
      <img
        :src="block.content.url"
        :alt="block.content.caption || 'Imagem'"
        class="image"
        @error="handleImageError"
      />
      
      <!-- Caption -->
      <div class="caption-area">
        <input
          v-model="caption"
          @blur="updateCaption"
          placeholder="Adicionar legenda..."
          class="caption-input"
        />
      </div>
      
      <!-- Image Actions -->
      <div class="image-actions">
        <button @click="replaceImage" class="action-btn">
          🔄 Substituir
        </button>
        <button @click="removeImage" class="action-btn danger">
          🗑️ Remover
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface ImageBlock {
  id: string
  type: 'image'
  content: {
    url: string
    caption: string
  }
}

interface Props {
  block: ImageBlock
}

interface Emits {
  update: [block: ImageBlock]
  delete: [id: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const fileInput = ref<HTMLInputElement | null>(null)
const showUrlInput = ref(false)
const imageUrl = ref('')
const caption = ref(props.block.content.caption || '')

function triggerFileUpload() {
  fileInput.value?.click()
}

function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  if (!file.type.startsWith('image/')) {
    alert('Por favor, selecione uma imagem válida')
    return
  }
  
  const reader = new FileReader()
  reader.onload = (e) => {
    const url = e.target?.result as string
    updateImage(url)
  }
  reader.readAsDataURL(file)
  
  // Reset input
  target.value = ''
}

function loadFromUrl() {
  if (!imageUrl.value.trim()) return
  
  try {
    new URL(imageUrl.value) // Validate URL
    updateImage(imageUrl.value)
    showUrlInput.value = false
    imageUrl.value = ''
  } catch {
    alert('URL inválida')
  }
}

function cancelUrl() {
  showUrlInput.value = false
  imageUrl.value = ''
}

function updateImage(url: string) {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      url
    }
  })
}

function updateCaption() {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      caption: caption.value
    }
  })
}

function replaceImage() {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      url: ''
    }
  })
}

function removeImage() {
  emit('delete', props.block.id)
}

function handleImageError() {
  alert('Erro ao carregar imagem')
  replaceImage()
}

// Sync caption with external changes
watch(() => props.block.content.caption, (newCaption) => {
  caption.value = newCaption || ''
})
</script>

<style scoped>
.image-block {
  margin: 15px 0;
}

/* Upload Area */
.upload-area {
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  background: #f7fafc;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #a0aec0;
  background: #edf2f7;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.upload-content h4 {
  margin: 0 0 15px 0;
  color: #4a5568;
  font-size: 1.2rem;
}

.upload-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 15px;
}

.upload-btn {
  background: #4299e1;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.upload-btn:hover {
  background: #3182ce;
}

.url-btn {
  background: #48bb78;
}

.url-btn:hover {
  background: #38a169;
}

/* URL Input */
.url-input-area {
  display: flex;
  gap: 8px;
  max-width: 400px;
  margin: 0 auto;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  outline: none;
}

.url-input:focus {
  border-color: #4299e1;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
}

.url-actions {
  display: flex;
  gap: 4px;
}

.url-action-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.url-action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Image Display */
.image-display {
  text-align: center;
}

.image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 10px;
}

.caption-area {
  margin-bottom: 15px;
}

.caption-input {
  width: 100%;
  max-width: 400px;
  padding: 8px 12px;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  background: transparent;
  text-align: center;
  font-size: 0.9rem;
  color: #718096;
  outline: none;
}

.caption-input:focus {
  border-bottom-color: #4299e1;
}

.image-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-btn {
  background: #edf2f7;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
}

.action-btn:hover {
  background: #e2e8f0;
}

.action-btn.danger:hover {
  background: #fed7d7;
  color: #e53e3e;
}
</style>
