<template>
  <div class="code-block" :class="`language-${language}`">
    <div class="code-block-header">
      <div class="language-selector">
        <select v-model="language" @change="onLanguageChange" class="language-select">
          <option value="python">Python</option>
          <option value="javascript">JavaScript</option>
          <option value="sql">SQL</option>
          <option value="bash">Bash</option>
          <option value="json">JSON</option>
          <option value="markdown">Markdown</option>
        </select>
      </div>
      
      <div class="code-actions">
        <button
          v-if="language === 'python'"
          @click="executeCode"
          :disabled="executing"
          class="action-btn execute-btn"
          title="Executar código (Ctrl+Enter)"
        >
          <PlayIcon class="action-icon" />
          {{ executing ? 'Executando...' : 'Executar' }}
        </button>
        
        <button
          @click="copyCode"
          class="action-btn copy-btn"
          title="Copiar código"
        >
          <CopyIcon class="action-icon" />
        </button>
        
        <button
          @click="toggleFullscreen"
          class="action-btn fullscreen-btn"
          title="Tela cheia (F11)"
        >
          <MaximizeIcon v-if="!isFullscreen" class="action-icon" />
          <MinimizeIcon v-else class="action-icon" />
        </button>
      </div>
    </div>
    
    <div 
      class="code-editor" 
      :class="{ 'fullscreen': isFullscreen }"
      ref="editorContainer"
    >
      <div class="line-numbers" v-if="showLineNumbers">
        <span 
          v-for="n in lineCount" 
          :key="n" 
          class="line-number"
          :class="{ 'current-line': n === currentLine }"
        >
          {{ n }}
        </span>
      </div>
      
      <textarea
        ref="codeEditor"
        v-model="code"
        @input="onCodeChange"
        @keydown="onKeyDown"
        @scroll="syncScroll"
        @click="updateCurrentLine"
        @keyup="updateCurrentLine"
        class="code-input"
        :placeholder="getPlaceholder()"
        spellcheck="false"
        autocomplete="off"
        autocapitalize="off"
        autocorrect="off"
      ></textarea>
      
      <div class="code-overlay" v-html="highlightedCode"></div>
    </div>
    
    <!-- Execution Result -->
    <div v-if="executionResult" class="execution-result">
      <div class="result-header">
        <span class="result-label">Resultado:</span>
        <div class="result-actions">
          <button
            @click="clearResult"
            class="clear-result-btn"
            title="Limpar resultado"
          >
            <XIcon class="clear-icon" />
          </button>
        </div>
      </div>
      
      <div class="result-content" :class="{ 'error': executionResult.error }">
        <pre v-if="executionResult.output">{{ executionResult.output }}</pre>
        <pre v-if="executionResult.error" class="error-output">{{ executionResult.error }}</pre>
        
        <div v-if="executionResult.plots && executionResult.plots.length > 0" class="plots-section">
          <h4>Gráficos:</h4>
          <div class="plots-grid">
            <div
              v-for="(plot, index) in executionResult.plots"
              :key="index"
              class="plot-container"
            >
              <img :src="plot" :alt="`Gráfico ${index + 1}`" class="plot-image" />
            </div>
          </div>
        </div>
      </div>
      
      <div class="result-footer">
        <span class="execution-time">
          Executado em {{ formatExecutionTime(executionResult.execution_time) }}
        </span>
      </div>
    </div>
    
    <!-- AI Assistant -->
    <div v-if="showAiAssistant" class="ai-assistant">
      <div class="ai-header">
        <div class="ai-title">
          <SparklesIcon class="ai-icon" />
          <span>Assistente de Código</span>
        </div>
        <button @click="toggleAiAssistant" class="close-ai-btn">
          <XIcon class="close-icon" />
        </button>
      </div>
      
      <div class="ai-actions">
        <button @click="explainCode" :disabled="aiLoading" class="ai-action-btn">
          <BookOpenIcon class="ai-action-icon" />
          Explicar código
        </button>
        
        <button @click="optimizeCode" :disabled="aiLoading" class="ai-action-btn">
          <ZapIcon class="ai-action-icon" />
          Otimizar
        </button>
        
        <button @click="fixCode" :disabled="aiLoading" class="ai-action-btn">
          <WrenchIcon class="ai-action-icon" />
          Corrigir erros
        </button>
        
        <button @click="addComments" :disabled="aiLoading" class="ai-action-btn">
          <MessageSquareIcon class="ai-action-icon" />
          Adicionar comentários
        </button>
      </div>
      
      <div v-if="aiResponse" class="ai-response">
        <div class="ai-response-header">Resposta do IA:</div>
        <div class="ai-response-content" v-html="formatAiResponse(aiResponse)"></div>
        
        <div v-if="aiSuggestion" class="ai-suggestion">
          <div class="suggestion-header">Código sugerido:</div>
          <pre class="suggestion-code">{{ aiSuggestion }}</pre>
          <div class="suggestion-actions">
            <button @click="applySuggestion" class="apply-btn">Aplicar</button>
            <button @click="dismissSuggestion" class="dismiss-btn">Descartar</button>
          </div>
        </div>
      </div>
      
      <div v-if="aiLoading" class="ai-loading">
        <div class="loading-spinner"></div>
        <span>Processando...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import {
  Play as PlayIcon,
  Copy as CopyIcon,
  Maximize as MaximizeIcon,
  Minimize as MinimizeIcon,
  X as XIcon,
  Sparkles as SparklesIcon,
  BookOpen as BookOpenIcon,
  Zap as ZapIcon,
  Wrench as WrenchIcon,
  MessageSquare as MessageSquareIcon
} from 'lucide-vue-next'

interface Props {
  initialCode?: string
  initialLanguage?: string
  showLineNumbers?: boolean
  readonly?: boolean
}

interface ExecutionResult {
  output?: string
  error?: string
  execution_time: number
  plots?: string[]
}

interface Emits {
  'update:code': [code: string]
  'update:language': [language: string]
  'execute': [code: string, language: string]
}

const props = withDefaults(defineProps<Props>(), {
  initialCode: '',
  initialLanguage: 'python',
  showLineNumbers: true,
  readonly: false
})

const emit = defineEmits<Emits>()

// Refs
const codeEditor = ref<HTMLTextAreaElement>()
const editorContainer = ref<HTMLDivElement>()

// State
const code = ref(props.initialCode)
const language = ref(props.initialLanguage)
const executing = ref(false)
const executionResult = ref<ExecutionResult | null>(null)
const isFullscreen = ref(false)
const currentLine = ref(1)
const showAiAssistant = ref(false)
const aiLoading = ref(false)
const aiResponse = ref('')
const aiSuggestion = ref('')

// Computed
const lineCount = computed(() => {
  return Math.max(1, code.value.split('\n').length)
})

const highlightedCode = computed(() => {
  if (!code.value) return ''
  return highlightSyntax(code.value, language.value)
})

// Methods
function onCodeChange() {
  emit('update:code', code.value)
  updateCurrentLine()
}

function onLanguageChange() {
  emit('update:language', language.value)
}

function updateCurrentLine() {
  if (!codeEditor.value) return
  
  const textarea = codeEditor.value
  const cursorPos = textarea.selectionStart
  const textBeforeCursor = code.value.substring(0, cursorPos)
  currentLine.value = textBeforeCursor.split('\n').length
}

function syncScroll() {
  if (!codeEditor.value) return
  
  const lineNumbers = document.querySelector('.line-numbers')
  const overlay = document.querySelector('.code-overlay')
  
  if (lineNumbers) {
    lineNumbers.scrollTop = codeEditor.value.scrollTop
  }
  
  if (overlay) {
    (overlay as HTMLElement).scrollTop = codeEditor.value.scrollTop
    (overlay as HTMLElement).scrollLeft = codeEditor.value.scrollLeft
  }
}

function onKeyDown(event: KeyboardEvent) {
  if (event.key === 'Tab') {
    event.preventDefault()
    insertTab()
  } else if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    if (language.value === 'python') {
      executeCode()
    }
  } else if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  }
}

function insertTab() {
  if (!codeEditor.value) return
  
  const textarea = codeEditor.value
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  
  // Insert tab character
  const newValue = code.value.substring(0, start) + '  ' + code.value.substring(end)
  code.value = newValue
  
  // Update cursor position
  nextTick(() => {
    textarea.setSelectionRange(start + 2, start + 2)
  })
}

async function executeCode() {
  if (executing.value || !code.value.trim()) return
  
  executing.value = true
  executionResult.value = null
  
  try {
    const response = await fetch('http://localhost:5002/api/code/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: code.value,
        language: language.value
      })
    })
    
    if (response.ok) {
      executionResult.value = await response.json()
    } else {
      const error = await response.json()
      executionResult.value = {
        error: error.error || 'Erro desconhecido',
        execution_time: 0
      }
    }
  } catch (error) {
    executionResult.value = {
      error: 'Erro de conexão com o servidor',
      execution_time: 0
    }
  } finally {
    executing.value = false
  }
  
  emit('execute', code.value, language.value)
}

async function copyCode() {
  try {
    await navigator.clipboard.writeText(code.value)
    showToast('Código copiado!', 'success')
  } catch (error) {
    showToast('Erro ao copiar código', 'error')
  }
}

function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    document.body.classList.add('code-block-fullscreen')
  } else {
    document.body.classList.remove('code-block-fullscreen')
  }
}

function clearResult() {
  executionResult.value = null
}

function toggleAiAssistant() {
  showAiAssistant.value = !showAiAssistant.value
  if (!showAiAssistant.value) {
    aiResponse.value = ''
    aiSuggestion.value = ''
  }
}

async function explainCode() {
  if (!code.value.trim()) return
  await callAiAssistant('explain', 'Explique o que este código faz:')
}

async function optimizeCode() {
  if (!code.value.trim()) return
  await callAiAssistant('optimize', 'Otimize este código:')
}

async function fixCode() {
  if (!code.value.trim()) return
  await callAiAssistant('fix', 'Corrija os erros neste código:')
}

async function addComments() {
  if (!code.value.trim()) return
  await callAiAssistant('comment', 'Adicione comentários explicativos a este código:')
}

async function callAiAssistant(action: string, prompt: string) {
  aiLoading.value = true
  aiResponse.value = ''
  aiSuggestion.value = ''
  
  try {
    const response = await fetch('http://localhost:5001/api/llm/complete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: `${prompt}\n\n\`\`\`${language.value}\n${code.value}\n\`\`\``,
        provider: 'openrouter',
        model: 'anthropic/claude-3-sonnet',
        max_tokens: 1000
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      aiResponse.value = result.response
      
      // Extract code suggestion if present
      const codeMatch = result.response.match(/```[\w]*\n([\s\S]*?)```/)
      if (codeMatch) {
        aiSuggestion.value = codeMatch[1].trim()
      }
    } else {
      const error = await response.json()
      aiResponse.value = `Erro: ${error.error || 'Falha ao conectar com IA'}`
    }
  } catch (error) {
    aiResponse.value = 'Erro de conexão com o serviço de IA'
  } finally {
    aiLoading.value = false
  }
}

function applySuggestion() {
  if (aiSuggestion.value) {
    code.value = aiSuggestion.value
    aiSuggestion.value = ''
    onCodeChange()
  }
}

function dismissSuggestion() {
  aiSuggestion.value = ''
}

function formatAiResponse(response: string): string {
  // Basic markdown to HTML conversion
  return response
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>')
}

function formatExecutionTime(time: number): string {
  if (time < 1) {
    return `${Math.round(time * 1000)}ms`
  }
  return `${time.toFixed(2)}s`
}

function getPlaceholder(): string {
  const placeholders = {
    python: 'print("Hello, World!")\n\n# Seu código Python aqui...',
    javascript: 'console.log("Hello, World!");\n\n// Seu código JavaScript aqui...',
    sql: 'SELECT * FROM tabela;\n\n-- Sua consulta SQL aqui...',
    bash: 'echo "Hello, World!"\n\n# Seus comandos bash aqui...',
    json: '{\n  "message": "Hello, World!"\n}',
    markdown: '# Título\n\nSeu texto markdown aqui...'
  }
  
  return placeholders[language.value as keyof typeof placeholders] || 'Digite seu código aqui...'
}

function highlightSyntax(code: string, lang: string): string {
  // Simplified syntax highlighting
  // In a real implementation, you'd use a library like Prism.js or highlight.js
  
  const keywords = {
    python: ['def', 'class', 'if', 'elif', 'else', 'for', 'while', 'import', 'from', 'return', 'try', 'except', 'with', 'as'],
    javascript: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export']
  }
  
  let highlighted = code
  const langKeywords = keywords[lang as keyof typeof keywords] || []
  
  // Highlight keywords
  langKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b(${keyword})\\b`, 'g')
    highlighted = highlighted.replace(regex, '<span class="keyword">$1</span>')
  })
  
  // Highlight strings
  highlighted = highlighted.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="string">$1$2$1</span>')
  
  // Highlight comments
  highlighted = highlighted.replace(/(#.*$)/gm, '<span class="comment">$1</span>')
  highlighted = highlighted.replace(/(\/\/.*$)/gm, '<span class="comment">$1</span>')
  
  return highlighted
}

function showToast(message: string, type: 'success' | 'error') {
  // Simple toast implementation
  const toast = document.createElement('div')
  toast.textContent = message
  toast.className = `toast toast-${type}`
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    background: ${type === 'success' ? '#22c55e' : '#e03e3e'};
    color: white;
    border-radius: 6px;
    font-weight: 500;
    z-index: 10000;
    animation: slideIn 0.3s ease;
  `
  
  document.body.appendChild(toast)
  
  setTimeout(() => {
    toast.remove()
  }, 3000)
}

// Watchers
watch(() => props.initialCode, (newCode) => {
  code.value = newCode
})

watch(() => props.initialLanguage, (newLang) => {
  language.value = newLang
})

// Lifecycle
onMounted(() => {
  if (codeEditor.value) {
    codeEditor.value.focus()
  }
})

onUnmounted(() => {
  document.body.classList.remove('code-block-fullscreen')
})
</script>

<style scoped>
.code-block {
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
  margin: 16px 0;
}

/* Header */
.code-block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f1f1ef;
  border-bottom: 1px solid #e9e9e7;
}

.language-select {
  border: none;
  background: transparent;
  font-size: 13px;
  color: #787774;
  cursor: pointer;
  outline: none;
}

.code-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  background: none;
  border-radius: 4px;
  font-size: 12px;
  color: #787774;
  cursor: pointer;
  transition: all 0.15s ease;
}

.action-btn:hover:not(:disabled) {
  background: rgba(55, 53, 47, 0.08);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.execute-btn {
  color: #2383e2;
  font-weight: 500;
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* Editor */
.code-editor {
  position: relative;
  display: flex;
  font-family: 'SF Mono', Monaco, 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  min-height: 120px;
  max-height: 400px;
  overflow: auto;
}

.code-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  max-height: none;
  background: #fafafa;
}

.line-numbers {
  display: flex;
  flex-direction: column;
  padding: 12px 8px;
  background: #f7f6f3;
  border-right: 1px solid #e9e9e7;
  color: #9b9a97;
  font-size: 13px;
  text-align: right;
  user-select: none;
  min-width: 40px;
  overflow: hidden;
}

.line-number {
  height: 21px;
  line-height: 21px;
}

.line-number.current-line {
  color: #37352f;
  font-weight: 500;
}

.code-input {
  flex: 1;
  padding: 12px;
  border: none;
  outline: none;
  resize: none;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: transparent;
  caret-color: #37352f;
  white-space: pre;
  overflow: hidden;
}

.code-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 12px;
  margin-left: 49px;
  pointer-events: none;
  white-space: pre;
  overflow: hidden;
  color: #37352f;
}

/* Syntax Highlighting */
.code-overlay :deep(.keyword) {
  color: #8b5cf6;
  font-weight: 500;
}

.code-overlay :deep(.string) {
  color: #22c55e;
}

.code-overlay :deep(.comment) {
  color: #9b9a97;
  font-style: italic;
}

/* Execution Result */
.execution-result {
  border-top: 1px solid #e9e9e7;
  background: white;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f7f6f3;
  border-bottom: 1px solid #e9e9e7;
}

.result-label {
  font-size: 13px;
  font-weight: 500;
  color: #37352f;
}

.clear-result-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.clear-result-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.clear-icon {
  width: 12px;
  height: 12px;
  color: #787774;
}

.result-content {
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.result-content pre {
  margin: 0;
  font-family: inherit;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.error-output {
  color: #e03e3e;
}

.plots-section {
  margin-top: 16px;
}

.plots-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #37352f;
}

.plots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.plot-image {
  width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-footer {
  padding: 8px 12px;
  border-top: 1px solid #f1f1ef;
  background: #fafafa;
}

.execution-time {
  font-size: 12px;
  color: #9b9a97;
}

/* AI Assistant */
.ai-assistant {
  border-top: 1px solid #e9e9e7;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.ai-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.ai-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #37352f;
}

.ai-icon {
  width: 16px;
  height: 16px;
  color: #8b5cf6;
}

.close-ai-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.close-ai-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.ai-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  padding: 12px;
}

.ai-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.ai-action-btn:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #8b5cf6;
}

.ai-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.ai-action-icon {
  width: 14px;
  height: 14px;
}

.ai-response {
  padding: 12px;
  border-top: 1px solid #e2e8f0;
}

.ai-response-header {
  font-weight: 500;
  color: #37352f;
  margin-bottom: 8px;
}

.ai-response-content {
  font-size: 14px;
  line-height: 1.5;
  color: #37352f;
  margin-bottom: 16px;
}

.ai-suggestion {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.suggestion-header {
  padding: 8px 12px;
  background: #f8fafc;
  font-size: 13px;
  font-weight: 500;
  color: #37352f;
  border-bottom: 1px solid #e2e8f0;
}

.suggestion-code {
  padding: 12px;
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 13px;
  line-height: 1.5;
  background: white;
  color: #37352f;
  white-space: pre-wrap;
  overflow-x: auto;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.apply-btn, .dismiss-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.apply-btn {
  background: #22c55e;
  color: white;
  border-color: #22c55e;
}

.apply-btn:hover {
  background: #16a34a;
}

.dismiss-btn {
  background: white;
  color: #787774;
}

.dismiss-btn:hover {
  background: #f7f6f3;
}

.ai-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 12px;
  color: #787774;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e9e9e7;
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Global fullscreen styles */
:global(body.code-block-fullscreen) {
  overflow: hidden;
}

/* Responsive */
@media (max-width: 768px) {
  .code-block-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .code-actions {
    justify-content: flex-end;
  }
  
  .ai-actions {
    grid-template-columns: 1fr;
  }
  
  .line-numbers {
    min-width: 30px;
  }
  
  .code-overlay {
    margin-left: 39px;
  }
}
</style>
