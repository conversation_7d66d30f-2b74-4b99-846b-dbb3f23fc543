<template>
  <div class="blocks-editor">
    <!-- Editor Header -->
    <div class="editor-header">
      <div class="editor-title">
        <input
          v-model="documentTitle"
          @blur="updateDocumentTitle"
          @keydown.enter="$event.target.blur()"
          class="title-input"
          placeholder="Documento sem título"
          maxlength="100"
        >
      </div>
      
      <div class="editor-actions">
        <button
          @click="saveDocument"
          class="action-btn action-btn--primary"
          :disabled="isSaving"
          title="Salvar documento"
        >
          <SaveIcon v-if="!isSaving" class="btn-icon" />
          <div v-else class="saving-spinner"></div>
          {{ isSaving ? 'Salvando...' : 'Salvar' }}
        </button>
        
        <button
          @click="exportDocument"
          class="action-btn"
          title="Exportar documento"
        >
          <DownloadIcon class="btn-icon" />
          Exportar
        </button>
        
        <div class="document-status">
          <span v-if="lastSaved" class="status-text">
            Salvo {{ formatLastSaved(lastSaved) }}
          </span>
          <div v-if="hasUnsavedChanges" class="unsaved-indicator" title="Alterações não salvas"></div>
        </div>
      </div>
    </div>
    
    <!-- Main Editor Content -->
    <div 
      class="editor-content"
      @click="handleEditorClick"
    >
      <!-- Empty State -->
      <div 
        v-if="blocks.length === 0"
        class="empty-state"
      >
        <div class="empty-icon">
          <FileTextIcon class="empty-icon-svg" />
        </div>
        <h3 class="empty-title">Comece a escrever</h3>
        <p class="empty-description">
          Pressione <kbd>/</kbd> para ver opções ou clique no botão abaixo para adicionar um bloco
        </p>
        <button
          @click="addInitialBlock"
          class="empty-action-btn"
        >
          <PlusIcon class="btn-icon" />
          Adicionar primeiro bloco
        </button>
      </div>
      
      <!-- Blocks Container -->
      <div 
        v-else
        class="blocks-container"
        ref="blocksContainer"
      >
        <!-- Block Items -->
        <div
          v-for="(block, index) in blocks"
          :key="block.id"
          class="block-wrapper"
          :class="{
            'block-wrapper--focused': focusedBlockId === block.id,
            'block-wrapper--dragging': dragState.dragging && dragState.draggedIndex === index,
            'block-wrapper--drag-over': dragState.dragOverIndex === index
          }"
          @mouseenter="handleBlockMouseEnter(block.id, index)"
          @mouseleave="handleBlockMouseLeave"
          @dragstart="handleDragStart($event, index)"
          @dragend="handleDragEnd"
          @dragover="handleDragOver($event, index)"
          @drop="handleDrop($event, index)"
          :draggable="!isEditingBlock(block.id)"
        >
          <!-- Drag Handle -->
          <div 
            class="drag-handle"
            :class="{ 'drag-handle--visible': hoveredBlockIndex === index }"
            @mousedown="prepareDrag"
            title="Arrastar para reordenar"
          >
            <GripVerticalIcon class="drag-icon" />
          </div>
          
          <!-- Add Block Button (appears between blocks) -->
          <div 
            v-if="hoveredBlockIndex === index"
            class="add-block-between"
          >
            <BlockMenu
              @selectBlock="(type) => addBlock(type, index)"
              :trigger-class="'add-btn-small'"
            >
              <template #trigger="{ toggle }">
                <button
                  @click="toggle"
                  class="add-btn-small"
                  title="Adicionar bloco"
                >
                  <PlusIcon class="add-icon" />
                </button>
              </template>
            </BlockMenu>
          </div>
          
          <!-- Block Content -->
          <div class="block-content">
            <!-- Text Block -->
            <TextBlock
              v-if="block.type === 'text'"
              :block="block"
              :index="index"
              @update="updateBlock"
              @delete="deleteBlock"
              @duplicate="duplicateBlock"
              @focus="setFocusedBlock"
              @createNewBlock="createNewBlockAfter"
              @keydown="handleBlockKeydown"
            />
            
            <!-- Image Block -->
            <ImageBlock
              v-else-if="block.type === 'image'"
              :block="block"
              :index="index"
              @update="updateBlock"
              @delete="deleteBlock"
              @duplicate="duplicateBlock"
              @focus="setFocusedBlock"
            />
            
            <!-- Link Block -->
            <LinkBlock
              v-else-if="block.type === 'link'"
              :block="block"
              :index="index"
              @update="updateBlock"
              @delete="deleteBlock"
              @duplicate="duplicateBlock"
              @focus="setFocusedBlock"
            />
            
            <!-- Code Block -->
            <CodeBlock
              v-else-if="block.type === 'code'"
              :block="block"
              :index="index"
              @update="updateBlock"
              @delete="deleteBlock"
              @duplicate="duplicateBlock"
              @focus="setFocusedBlock"
            />
          </div>
        </div>
        
        <!-- Final Add Block Button -->
        <div class="add-block-final">
          <BlockMenu
            @selectBlock="(type) => addBlock(type)"
            :trigger-class="'add-btn-final'"
          >
            <template #trigger="{ toggle }">
              <button
                @click="toggle"
                class="add-btn-final"
                title="Adicionar bloco"
              >
                <PlusIcon class="add-icon" />
                <span>Adicionar bloco</span>
              </button>
            </template>
          </BlockMenu>
        </div>
      </div>
    </div>
    
    <!-- Command Palette (Slash Commands) -->
    <div
      v-if="commandPalette.show"
      class="command-palette"
      :style="{
        top: `${commandPalette.position.y}px`,
        left: `${commandPalette.position.x}px`
      }"
    >
      <div class="palette-header">
        <span class="palette-title">Inserir bloco</span>
        <button
          @click="hideCommandPalette"
          class="palette-close"
        >
          <XIcon class="close-icon" />
        </button>
      </div>
      
      <div class="palette-content">
        <div
          v-for="(group, groupName) in groupedCommands"
          :key="groupName"
          class="command-group"
        >
          <h4 class="group-title">{{ groupName }}</h4>
          <div
            v-for="(command, index) in group"
            :key="command.type"
            class="command-item"
            :class="{ 'command-item--selected': selectedCommandIndex === getCommandGlobalIndex(groupName, index) }"
            @click="executeCommand(command.type)"
            @mouseenter="selectedCommandIndex = getCommandGlobalIndex(groupName, index)"
          >
            <component :is="command.icon" class="command-icon" />
            <div class="command-info">
              <span class="command-name">{{ command.name }}</span>
              <span class="command-description">{{ command.description }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Drag Preview -->
    <div
      v-if="dragState.dragging"
      class="drag-preview"
      :style="{
        top: `${dragState.previewPosition.y}px`,
        left: `${dragState.previewPosition.x}px`
      }"
    >
      <div class="drag-preview-content">
        {{ getDragPreviewText(dragState.draggedIndex) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import {
  Plus as PlusIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  FileText as FileTextIcon,
  GripVertical as GripVerticalIcon,
  X as XIcon,
  Type as TypeIcon,
  Heading1 as Heading1Icon,
  Heading2 as Heading2Icon,
  List as ListIcon,
  Quote as QuoteIcon,
  Minus as MinusIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Code as CodeIcon
} from 'lucide-vue-next'
import { v4 as uuidv4 } from 'uuid'

// Import block components
import BlockMenu from './BlockMenuSimple.vue'
import TextBlock from './blocks/TextBlock.vue'
import ImageBlock from './blocks/ImageBlock.vue'
import LinkBlock from './blocks/LinkBlock.vue'
import CodeBlock from './blocks/CodeBlock.vue'

// Types
export interface BaseBlock {
  id: string
  type: string
  createdAt: Date
  updatedAt: Date
}

export interface Document {
  id: string
  title: string
  blocks: BaseBlock[]
  createdAt: Date
  updatedAt: Date
}

interface DragState {
  dragging: boolean
  draggedIndex: number
  dragOverIndex: number
  previewPosition: { x: number; y: number }
}

interface CommandPalette {
  show: boolean
  position: { x: number; y: number }
  blockIndex: number
}

interface Command {
  type: string
  name: string
  description: string
  icon: any
  group: string
}

// Props
interface Props {
  documentId?: string
  initialBlocks?: BaseBlock[]
  initialTitle?: string
}

interface Emits {
  save: [document: Document]
  export: [document: Document]
}

const props = withDefaults(defineProps<Props>(), {
  documentId: () => uuidv4(),
  initialBlocks: () => [],
  initialTitle: ''
})

const emit = defineEmits<Emits>()

// State
const blocks = ref<BaseBlock[]>([])
const documentTitle = ref('')
const focusedBlockId = ref<string | null>(null)
const hoveredBlockIndex = ref<number | null>(null)
const isSaving = ref(false)
const hasUnsavedChanges = ref(false)
const lastSaved = ref<Date | null>(null)
const blocksContainer = ref<HTMLElement | null>(null)

// Drag and Drop State
const dragState = ref<DragState>({
  dragging: false,
  draggedIndex: -1,
  dragOverIndex: -1,
  previewPosition: { x: 0, y: 0 }
})

// Command Palette State
const commandPalette = ref<CommandPalette>({
  show: false,
  position: { x: 0, y: 0 },
  blockIndex: -1
})
const selectedCommandIndex = ref(0)

// Commands Definition
const commands: Command[] = [
  // Basic
  { type: 'text', name: 'Texto', description: 'Parágrafo simples', icon: TypeIcon, group: 'Básico' },
  { type: 'heading1', name: 'Título 1', description: 'Título principal', icon: Heading1Icon, group: 'Básico' },
  { type: 'heading2', name: 'Título 2', description: 'Subtítulo', icon: Heading2Icon, group: 'Básico' },
  { type: 'bulletList', name: 'Lista', description: 'Lista com marcadores', icon: ListIcon, group: 'Básico' },
  { type: 'quote', name: 'Citação', description: 'Bloco de citação', icon: QuoteIcon, group: 'Básico' },
  { type: 'divider', name: 'Divisor', description: 'Linha divisória', icon: MinusIcon, group: 'Básico' },
  
  // Media
  { type: 'image', name: 'Imagem', description: 'Upload ou URL de imagem', icon: ImageIcon, group: 'Mídia' },
  { type: 'link', name: 'Link', description: 'Link com preview', icon: LinkIcon, group: 'Mídia' },
  
  // Advanced
  { type: 'code', name: 'Código Python', description: 'Bloco de código executável', icon: CodeIcon, group: 'Avançado' }
]

// Computed
const groupedCommands = computed(() => {
  const grouped: Record<string, Command[]> = {}
  commands.forEach(command => {
    if (!grouped[command.group]) {
      grouped[command.group] = []
    }
    grouped[command.group].push(command)
  })
  return grouped
})

const flatCommands = computed(() => {
  return Object.values(groupedCommands.value).flat()
})

// Methods
function createBlock(type: string, content: any = {}): BaseBlock {
  const now = new Date()
  return {
    id: uuidv4(),
    type,
    content: getDefaultContent(type, content),
    createdAt: now,
    updatedAt: now
  } as BaseBlock
}

function getDefaultContent(type: string, override: any = {}) {
  const defaults = {
    text: { text: '', subtype: 'paragraph' },
    heading1: { text: '', subtype: 'heading1' },
    heading2: { text: '', subtype: 'heading2' },
    bulletList: { items: [''], subtype: 'bulletList' },
    quote: { text: '', subtype: 'quote' },
    divider: { subtype: 'divider' },
    image: { url: '', caption: '', alt: '', width: 400, height: 300 },
    link: { url: '', title: '', description: '', favicon: '', thumbnail: '', domain: '' },
    code: { code: '', language: 'python', output: '', success: false }
  }
  
  return { ...defaults[type as keyof typeof defaults], ...override }
}

function addBlock(type: string, index?: number) {
  console.log('addBlock called with type:', type, 'index:', index)
  const block = createBlock(type)
  console.log('created block:', block)
  const insertIndex = index !== undefined ? index + 1 : blocks.value.length
  
  blocks.value.splice(insertIndex, 0, block)
  console.log('blocks after adding:', blocks.value.length)
  hasUnsavedChanges.value = true
  
  nextTick(() => {
    setFocusedBlock(block.id)
  })
  
  hideCommandPalette()
}

function addInitialBlock() {
  console.log('addInitialBlock called')
  addBlock('text')
}

function updateBlock(updatedBlock: BaseBlock) {
  const index = blocks.value.findIndex(block => block.id === updatedBlock.id)
  if (index !== -1) {
    blocks.value[index] = {
      ...updatedBlock,
      updatedAt: new Date()
    }
    hasUnsavedChanges.value = true
  }
}

function deleteBlock(blockId: string) {
  const index = blocks.value.findIndex(block => block.id === blockId)
  if (index !== -1) {
    blocks.value.splice(index, 1)
    hasUnsavedChanges.value = true
    
    // Focus next or previous block
    if (blocks.value.length > 0) {
      const newFocusIndex = Math.min(index, blocks.value.length - 1)
      setFocusedBlock(blocks.value[newFocusIndex].id)
    } else {
      focusedBlockId.value = null
    }
  }
}

function duplicateBlock(block: BaseBlock) {
  const index = blocks.value.findIndex(b => b.id === block.id)
  if (index !== -1) {
    const duplicatedBlock = createBlock(block.type, block.content)
    blocks.value.splice(index + 1, 0, duplicatedBlock)
    hasUnsavedChanges.value = true
    
    nextTick(() => {
      setFocusedBlock(duplicatedBlock.id)
    })
  }
}

function setFocusedBlock(blockId: string) {
  focusedBlockId.value = blockId
}

function isEditingBlock(blockId: string): boolean {
  return focusedBlockId.value === blockId
}

function createNewBlockAfter(blockId: string) {
  const index = blocks.value.findIndex(block => block.id === blockId)
  if (index !== -1) {
    addBlock('text', index)
  }
}

function handleBlockMouseEnter(blockId: string, index: number) {
  hoveredBlockIndex.value = index
}

function handleBlockMouseLeave() {
  hoveredBlockIndex.value = null
}

function handleEditorClick(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.block-wrapper') && !target.closest('.command-palette')) {
    focusedBlockId.value = null
    hideCommandPalette()
  }
}

function handleBlockKeydown(event: KeyboardEvent, blockId: string, blockIndex: number) {
  // Handle slash commands
  if (event.key === '/' && !event.shiftKey && !event.ctrlKey && !event.metaKey) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()
      
      showCommandPalette({
        x: rect.left,
        y: rect.bottom + window.scrollY
      }, blockIndex)
    }
  }
  
  // Navigation between blocks
  if (event.key === 'ArrowUp' && event.metaKey) {
    event.preventDefault()
    if (blockIndex > 0) {
      setFocusedBlock(blocks.value[blockIndex - 1].id)
    }
  }
  
  if (event.key === 'ArrowDown' && event.metaKey) {
    event.preventDefault()
    if (blockIndex < blocks.value.length - 1) {
      setFocusedBlock(blocks.value[blockIndex + 1].id)
    }
  }
}

// Command Palette
function showCommandPalette(position: { x: number; y: number }, blockIndex: number) {
  commandPalette.value = {
    show: true,
    position,
    blockIndex
  }
  selectedCommandIndex.value = 0
}

function hideCommandPalette() {
  commandPalette.value.show = false
  selectedCommandIndex.value = 0
}

function executeCommand(type: string) {
  addBlock(type, commandPalette.value.blockIndex)
}

function getCommandGlobalIndex(groupName: string, indexInGroup: number): number {
  let globalIndex = 0
  const groups = Object.keys(groupedCommands.value)
  
  for (const group of groups) {
    if (group === groupName) {
      return globalIndex + indexInGroup
    }
    globalIndex += groupedCommands.value[group].length
  }
  
  return 0
}

// Drag and Drop
function prepareDrag() {
  // Just visual feedback - actual drag starts in handleDragStart
}

function handleDragStart(event: DragEvent, index: number) {
  dragState.value = {
    dragging: true,
    draggedIndex: index,
    dragOverIndex: -1,
    previewPosition: { x: event.clientX, y: event.clientY }
  }
  
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', index.toString())
  }
}

function handleDragEnd() {
  dragState.value = {
    dragging: false,
    draggedIndex: -1,
    dragOverIndex: -1,
    previewPosition: { x: 0, y: 0 }
  }
}

function handleDragOver(event: DragEvent, index: number) {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
  
  dragState.value.dragOverIndex = index
}

function handleDrop(event: DragEvent, dropIndex: number) {
  event.preventDefault()
  
  const draggedIndex = dragState.value.draggedIndex
  if (draggedIndex === dropIndex || draggedIndex === -1) return
  
  // Reorder blocks
  const draggedBlock = blocks.value[draggedIndex]
  blocks.value.splice(draggedIndex, 1)
  
  const newIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex
  blocks.value.splice(newIndex, 0, draggedBlock)
  
  hasUnsavedChanges.value = true
  handleDragEnd()
}

function getDragPreviewText(index: number): string {
  const block = blocks.value[index]
  if (!block) return ''
  
  switch (block.type) {
    case 'text':
      return block.content.text || 'Bloco de texto'
    case 'image':
      return 'Bloco de imagem'
    case 'link':
      return `Link: ${block.content.url || 'URL'}`
    case 'code':
      return 'Bloco de código'
    default:
      return 'Bloco'
  }
}

// Document Management
function updateDocumentTitle() {
  hasUnsavedChanges.value = true
}

async function saveDocument() {
  if (isSaving.value) return
  
  isSaving.value = true
  
  try {
    const document: Document = {
      id: props.documentId,
      title: documentTitle.value || 'Documento sem título',
      blocks: blocks.value,
      createdAt: blocks.value[0]?.createdAt || new Date(),
      updatedAt: new Date()
    }
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('save', document)
    hasUnsavedChanges.value = false
    lastSaved.value = new Date()
    
  } catch (error) {
    console.error('Error saving document:', error)
  } finally {
    isSaving.value = false
  }
}

function exportDocument() {
  const document: Document = {
    id: props.documentId,
    title: documentTitle.value || 'Documento sem título',
    blocks: blocks.value,
    createdAt: blocks.value[0]?.createdAt || new Date(),
    updatedAt: new Date()
  }
  
  emit('export', document)
}

function formatLastSaved(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / 60000)
  
  if (diffMinutes === 0) return 'agora'
  if (diffMinutes === 1) return 'há 1 minuto'
  if (diffMinutes < 60) return `há ${diffMinutes} minutos`
  
  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours === 1) return 'há 1 hora'
  if (diffHours < 24) return `há ${diffHours} horas`
  
  return date.toLocaleDateString('pt-BR')
}

// Global Event Handlers
function handleGlobalKeydown(event: KeyboardEvent) {
  // Command palette navigation
  if (commandPalette.value.show) {
    if (event.key === 'ArrowDown') {
      event.preventDefault()
      selectedCommandIndex.value = Math.min(
        selectedCommandIndex.value + 1,
        flatCommands.value.length - 1
      )
    } else if (event.key === 'ArrowUp') {
      event.preventDefault()
      selectedCommandIndex.value = Math.max(selectedCommandIndex.value - 1, 0)
    } else if (event.key === 'Enter') {
      event.preventDefault()
      const command = flatCommands.value[selectedCommandIndex.value]
      if (command) {
        executeCommand(command.type)
      }
    } else if (event.key === 'Escape') {
      hideCommandPalette()
    }
  }
  
  // Save shortcut
  if ((event.metaKey || event.ctrlKey) && event.key === 's') {
    event.preventDefault()
    saveDocument()
  }
}

function handleGlobalClick(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.command-palette') && commandPalette.value.show) {
    hideCommandPalette()
  }
}

// Initialize
function initializeEditor() {
  if (props.initialBlocks.length > 0) {
    blocks.value = [...props.initialBlocks]
  }
  
  if (props.initialTitle) {
    documentTitle.value = props.initialTitle
  }
}

// Watchers
watch(blocks, () => {
  hasUnsavedChanges.value = true
}, { deep: true })

// Lifecycle
onMounted(() => {
  initializeEditor()
  document.addEventListener('keydown', handleGlobalKeydown)
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
  document.removeEventListener('click', handleGlobalClick)
})
</script>

<style scoped>
.blocks-editor {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
  min-height: calc(100vh - 48px);
  background: white;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 1;
}

/* Editor Header */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9e9e7;
}

.editor-title {
  flex: 1;
  margin-right: 20px;
}

.title-input {
  font-size: 42px;
  font-weight: 700;
  color: #37352f;
  border: none;
  outline: none;
  background: transparent;
  width: 100%;
  line-height: 1.2;
  font-family: inherit;
}

.title-input::placeholder {
  color: #9b9a97;
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  background: white;
  color: #37352f;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.action-btn:hover:not(:disabled) {
  background: #f7f6f3;
  border-color: #d3d1cb;
}

.action-btn--primary {
  background: #2383e2;
  border-color: #2383e2;
  color: white;
}

.action-btn--primary:hover:not(:disabled) {
  background: #1a6bb8;
}

.action-btn:disabled {
  background: #f7f6f3;
  color: #9b9a97;
  cursor: not-allowed;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.saving-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.document-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #787774;
}

.unsaved-indicator {
  width: 8px;
  height: 8px;
  background: #f59e0b;
  border-radius: 50%;
  title: "Alterações não salvas";
}

/* Editor Content */
.editor-content {
  position: relative;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  margin: 0 auto 20px;
  width: 80px;
  height: 80px;
  background: #f7f6f3;
  border-radius: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon-svg {
  width: 32px;
  height: 32px;
  color: #9b9a97;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #37352f;
  margin: 0 0 8px;
}

.empty-description {
  font-size: 16px;
  color: #787774;
  margin: 0 0 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-description kbd {
  background: #f7f6f3;
  border: 1px solid #e9e9e7;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 14px;
  font-family: 'SF Mono', Monaco, monospace;
  color: #37352f;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #2383e2;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.empty-action-btn:hover {
  background: #1a6bb8;
}

/* Blocks Container */
.blocks-container {
  position: relative;
}

.block-wrapper {
  position: relative;
  margin: 2px 0;
  border-radius: 6px;
  transition: all 0.15s ease;
}

.block-wrapper--focused {
  background: rgba(35, 131, 226, 0.04);
}

.block-wrapper--dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.block-wrapper--drag-over {
  border-top: 3px solid #2383e2;
}

.block-content {
  position: relative;
}

/* Drag Handle */
.drag-handle {
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.15s ease;
  background: white;
  border: 1px solid #e9e9e7;
}

.drag-handle--visible {
  opacity: 1;
}

.drag-handle:hover {
  background: #f7f6f3;
}

.drag-handle:active {
  cursor: grabbing;
}

.drag-icon {
  width: 14px;
  height: 14px;
  color: #9b9a97;
}

/* Add Block Buttons */
.add-block-between {
  position: absolute;
  left: -40px;
  top: -8px;
  z-index: 10;
}

.add-btn-small {
  width: 24px;
  height: 24px;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s ease;
}

.add-btn-small:hover {
  background: #2383e2;
  border-color: #2383e2;
}

.add-btn-small:hover .add-icon {
  color: white;
}

.add-icon {
  width: 12px;
  height: 12px;
  color: #9b9a97;
}

.add-block-final {
  margin-top: 20px;
  text-align: center;
}

.add-btn-final {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  color: #787774;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.add-btn-final:hover {
  background: #f7f6f3;
  border-color: #d3d1cb;
  color: #37352f;
}

/* Command Palette */
.command-palette {
  position: absolute;
  z-index: 1000;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  box-shadow: 0 16px 70px rgba(0, 0, 0, 0.2);
  min-width: 320px;
  max-height: 400px;
  overflow: hidden;
}

.palette-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e9e9e7;
  background: #f7f6f3;
}

.palette-title {
  font-size: 14px;
  font-weight: 600;
  color: #37352f;
}

.palette-close {
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}

.close-icon {
  width: 14px;
  height: 14px;
  color: #9b9a97;
}

.palette-content {
  max-height: 340px;
  overflow-y: auto;
  padding: 8px 0;
}

.command-group {
  margin-bottom: 16px;
}

.command-group:last-child {
  margin-bottom: 0;
}

.group-title {
  padding: 8px 16px 4px;
  font-size: 12px;
  font-weight: 600;
  color: #787774;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

.command-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.command-item--selected,
.command-item:hover {
  background: rgba(35, 131, 226, 0.08);
}

.command-icon {
  width: 18px;
  height: 18px;
  color: #787774;
  flex-shrink: 0;
}

.command-info {
  flex: 1;
  min-width: 0;
}

.command-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #37352f;
  margin-bottom: 2px;
}

.command-description {
  display: block;
  font-size: 12px;
  color: #787774;
}

/* Drag Preview */
.drag-preview {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  transform: translate(-50%, -50%);
}

.drag-preview-content {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive */
@media (max-width: 768px) {
  .blocks-editor {
    padding: 20px 16px;
  }
  
  .editor-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .editor-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .title-input {
    font-size: 32px;
  }
  
  .drag-handle,
  .add-block-between {
    display: none;
  }
  
  .command-palette {
    min-width: 280px;
    left: 16px !important;
    right: 16px;
    width: auto !important;
  }
}

/* Scrollbar */
.palette-content::-webkit-scrollbar {
  width: 6px;
}

.palette-content::-webkit-scrollbar-track {
  background: transparent;
}

.palette-content::-webkit-scrollbar-thumb {
  background: #e9e9e7;
  border-radius: 3px;
}

.palette-content::-webkit-scrollbar-thumb:hover {
  background: #d3d1cb;
}
</style>
