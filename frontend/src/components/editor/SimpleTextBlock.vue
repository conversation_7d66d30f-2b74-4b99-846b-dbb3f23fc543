<template>
  <div class="text-block">
    <div v-if="block.content.subtype === 'heading1'" class="heading-1">
      <input
        v-model="text"
        @blur="updateContent"
        @keydown.enter="handleEnter"
        placeholder="Título 1..."
        class="text-input heading-1-input"
      />
    </div>
    
    <div v-else-if="block.content.subtype === 'heading2'" class="heading-2">
      <input
        v-model="text"
        @blur="updateContent"
        @keydown.enter="handleEnter"
        placeholder="Título 2..."
        class="text-input heading-2-input"
      />
    </div>
    
    <div v-else-if="block.content.subtype === 'quote'" class="quote">
      <textarea
        v-model="text"
        @blur="updateContent"
        @keydown.enter="handleEnter"
        placeholder="Escreva uma citação..."
        class="text-input quote-input"
        rows="2"
      ></textarea>
    </div>
    
    <div v-else class="paragraph">
      <textarea
        v-model="text"
        @blur="updateContent"
        @keydown.enter="handleEnter"
        placeholder="Escreva algo..."
        class="text-input paragraph-input"
        rows="3"
      ></textarea>
    </div>

    <!-- Format Toolbar -->
    <div v-if="showToolbar" class="format-toolbar">
      <button @click="changeType('paragraph')" :class="{ active: block.content.subtype === 'paragraph' }">
        📄 Texto
      </button>
      <button @click="changeType('heading1')" :class="{ active: block.content.subtype === 'heading1' }">
        📰 H1
      </button>
      <button @click="changeType('heading2')" :class="{ active: block.content.subtype === 'heading2' }">
        📑 H2
      </button>
      <button @click="changeType('quote')" :class="{ active: block.content.subtype === 'quote' }">
        💭 Citação
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface TextBlock {
  id: string
  type: 'text'
  content: {
    text: string
    subtype: 'paragraph' | 'heading1' | 'heading2' | 'quote'
  }
}

interface Props {
  block: TextBlock
}

interface Emits {
  update: [block: TextBlock]
  delete: [id: string]
  addAfter: [id: string, type: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const text = ref(props.block.content.text || '')
const showToolbar = ref(false)

function updateContent() {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      text: text.value
    }
  })
  showToolbar.value = false
}

function handleEnter(event: KeyboardEvent) {
  if (!event.shiftKey) {
    event.preventDefault()
    updateContent()
    emit('addAfter', props.block.id, 'text')
  }
}

function changeType(subtype: TextBlock['content']['subtype']) {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      subtype
    }
  })
  showToolbar.value = false
}

function handleFocus() {
  showToolbar.value = true
}

// Sincroniza com mudanças externas
watch(() => props.block.content.text, (newText) => {
  text.value = newText || ''
})
</script>

<style scoped>
.text-block {
  position: relative;
  margin: 10px 0;
}

.text-input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: vertical;
  font-family: inherit;
  line-height: 1.6;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.text-input:focus {
  background: rgba(0, 0, 0, 0.02);
}

.paragraph-input {
  font-size: 1rem;
  color: #2d3748;
  min-height: 50px;
}

.heading-1-input {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  min-height: 60px;
}

.heading-2-input {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  min-height: 50px;
}

.quote-input {
  font-size: 1.1rem;
  font-style: italic;
  color: #4a5568;
  border-left: 4px solid #4299e1;
  padding-left: 16px;
  background: rgba(66, 153, 225, 0.05);
}

/* Format Toolbar */
.format-toolbar {
  position: absolute;
  top: -45px;
  left: 0;
  background: white;
  padding: 5px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #e2e8f0;
  display: flex;
  gap: 5px;
  z-index: 10;
}

.format-toolbar button {
  background: none;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  white-space: nowrap;
}

.format-toolbar button:hover {
  background: #f7fafc;
}

.format-toolbar button.active {
  background: #4299e1;
  color: white;
}

/* Placeholders */
::placeholder {
  color: #a0aec0;
  opacity: 1;
}
</style>
