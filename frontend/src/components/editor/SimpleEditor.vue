<template>
  <div class="simple-editor">
    <div 
      ref="editorRef"
      contenteditable="true" 
      class="editor-content"
      @input="handleInput"
      placeholder="Digite seu conteúdo aqui..."
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  content?: string
}

interface Emits {
  update: [content: string]
  save: [content: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const editorRef = ref<HTMLElement | null>(null)

function handleInput() {
  if (!editorRef.value) return
  
  const content = editorRef.value.innerHTML
  emit('update', content)
}

// Watchers
watch(() => props.content, (newContent) => {
  if (newContent && editorRef.value && editorRef.value.innerHTML !== newContent) {
    editorRef.value.innerHTML = newContent
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  if (props.content && editorRef.value) {
    editorRef.value.innerHTML = props.content
  }
})
</script>

<style scoped>
.simple-editor {
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  background: white;
  min-height: 400px;
}

.editor-content {
  padding: 24px;
  min-height: 376px;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #37352f;
}

.editor-content:empty::before {
  content: attr(placeholder);
  color: #c9c7c4;
  pointer-events: none;
}
</style>
