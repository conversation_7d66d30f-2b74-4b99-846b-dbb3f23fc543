<template>
  <div class="enhanced-editor">
    <!-- Toolbar -->
    <div class="editor-toolbar" v-if="!readonly">
      <div class="toolbar-group">
        <button
          v-for="format in textFormats"
          :key="format.name"
          @click="applyFormat(format.name)"
          :class="{ active: activeFormats.includes(format.name) }"
          class="toolbar-btn"
          :title="format.title"
        >
          <component :is="format.icon" class="toolbar-icon" />
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button
          v-for="block in blockTypes"
          :key="block.name"
          @click="insertBlock(block.name)"
          class="toolbar-btn"
          :title="block.title"
        >
          <component :is="block.icon" class="toolbar-icon" />
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button
          @click="toggleAiAssistant"
          class="toolbar-btn ai-btn"
          title="Assistente IA"
        >
          <SparklesIcon class="toolbar-icon" />
        </button>
        
        <button
          v-if="connected"
          class="toolbar-btn connected"
          :title="`${collaborators.length} usuário(s) online`"
        >
          <UsersIcon class="toolbar-icon" />
          <span v-if="collaborators.length > 0" class="collaborator-count">{{ collaborators.length }}</span>
        </button>
      </div>
    </div>
    
    <!-- Collaborators Cursors -->
    <div class="collaborators-cursors">
      <div
        v-for="user in collaborators"
        :key="user.id"
        class="cursor"
        :style="{ top: user.cursor_y + 'px', left: user.cursor_x + 'px' }"
      >
        <div class="cursor-line" :style="{ backgroundColor: user.color }"></div>
        <div class="cursor-label" :style="{ backgroundColor: user.color }">
          {{ user.name }}
        </div>
      </div>
    </div>
    
    <!-- Main Editor Area -->
    <div class="editor-container" :class="{ readonly }">
      <div
        ref="editorRef"
        class="editor-content"
        :contenteditable="!readonly"
        @input="handleInput"
        @keydown="handleKeydown"
        @keyup="handleKeyup"
        @mouseup="handleMouseUp"
        @focus="handleFocus"
        @blur="handleBlur"
        @paste="handlePaste"
        :placeholder="placeholder"
      ></div>
      
      <!-- Dynamic Code Blocks -->
      <div v-for="block in codeBlocks" :key="block.id" class="dynamic-code-block">
        <CodeBlock
          :initial-code="block.content"
          :initial-language="block.language"
          :show-line-numbers="true"
          :readonly="readonly"
          @update:code="updateCodeBlock(block.id, $event)"
          @update:language="updateCodeBlockLanguage(block.id, $event)"
          @execute="onCodeExecute(block.id, $event)"
        />
        <button
          v-if="!readonly"
          @click="removeCodeBlock(block.id)"
          class="remove-block-btn"
          title="Remover bloco"
        >
          <XIcon class="remove-icon" />
        </button>
      </div>
    </div>
    
    <!-- AI Assistant Panel -->
    <div v-if="showAiPanel" class="ai-assistant-panel">
      <div class="ai-panel-header">
        <div class="ai-title">
          <SparklesIcon class="ai-icon" />
          <span>Assistente de Escrita</span>
        </div>
        <button @click="toggleAiAssistant" class="close-ai-btn">
          <XIcon class="close-icon" />
        </button>
      </div>
      
      <div class="ai-panel-content">
        <div class="ai-actions">
          <button @click="improveText" :disabled="aiLoading" class="ai-action-btn">
            <EditIcon class="ai-action-icon" />
            Melhorar Texto
          </button>
          
          <button @click="summarizeText" :disabled="aiLoading" class="ai-action-btn">
            <FileTextIcon class="ai-action-icon" />
            Resumir
          </button>
          
          <button @click="translateText" :disabled="aiLoading" class="ai-action-btn">
            <LanguagesIcon class="ai-action-icon" />
            Traduzir
          </button>
          
          <button @click="continueWriting" :disabled="aiLoading" class="ai-action-btn">
            <PenToolIcon class="ai-action-icon" />
            Continuar Escrevendo
          </button>
        </div>
        
        <div class="ai-input-group">
          <textarea
            v-model="aiPrompt"
            placeholder="Descreva o que você quer que eu faça com o texto..."
            class="ai-textarea"
            rows="3"
          ></textarea>
          <button
            @click="customAiAction"
            :disabled="aiLoading || !aiPrompt.trim()"
            class="ai-submit-btn"
          >
            {{ aiLoading ? 'Processando...' : 'Executar' }}
          </button>
        </div>
        
        <div v-if="aiResponse" class="ai-response">
          <div class="ai-response-header">Sugestão:</div>
          <div class="ai-response-content" v-html="formatAiResponse(aiResponse)"></div>
          <div class="ai-response-actions">
            <button @click="applyAiSuggestion" class="apply-btn">Aplicar</button>
            <button @click="dismissAiSuggestion" class="dismiss-btn">Descartar</button>
          </div>
        </div>
        
        <div v-if="aiLoading" class="ai-loading">
          <div class="loading-spinner"></div>
          <span>IA processando...</span>
        </div>
      </div>
    </div>
    
    <!-- Block Insert Menu -->
    <div v-if="showBlockMenu" class="block-menu" :style="blockMenuStyle">
      <div class="block-menu-search">
        <input
          v-model="blockSearchQuery"
          placeholder="Buscar blocos..."
          class="block-search-input"
          @keydown.escape="hideBlockMenu"
        />
      </div>
      
      <div class="block-menu-items">
        <div
          v-for="block in filteredBlockTypes"
          :key="block.name"
          @click="insertBlock(block.name)"
          class="block-menu-item"
        >
          <component :is="block.icon" class="block-menu-icon" />
          <div>
            <div class="block-menu-title">{{ block.title }}</div>
            <div class="block-menu-description">{{ block.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import {
  Bold as BoldIcon,
  Italic as ItalicIcon,
  Underline as UnderlineIcon,
  Code as CodeIcon,
  Heading1 as Heading1Icon,
  Heading2 as Heading2Icon,
  List as ListIcon,
  CheckSquare as CheckSquareIcon,
  Quote as QuoteIcon,
  Divide as DivideIcon,
  Image as ImageIcon,
  Table as TableIcon,
  Calculator as CalculatorIcon,
  Sparkles as SparklesIcon,
  Users as UsersIcon,
  X as XIcon,
  Edit as EditIcon,
  FileText as FileTextIcon,
  Languages as LanguagesIcon,
  PenTool as PenToolIcon,
  Terminal as TerminalIcon
} from 'lucide-vue-next'
import CodeBlock from './CodeBlock.vue'
import { useCollaborativeEditing } from '@/composables/useWebSocket'

interface Props {
  content?: string
  placeholder?: string
  readonly?: boolean
  pageId?: number
}

interface CodeBlockData {
  id: string
  content: string
  language: string
  position: number
}

interface Emits {
  'update': [content: string]
  'save': [content: string]
  'code-execute': [blockId: string, code: string, language: string]
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  placeholder: 'Comece a escrever ou digite "/" para ver os comandos...',
  readonly: false,
  pageId: 0
})

const emit = defineEmits<Emits>()

// Collaborative editing
const {
  connected,
  users: collaborators,
  join,
  leave,
  broadcastUpdate,
  broadcastCursor,
  isReceivingUpdate,
  pendingChanges
} = props.pageId ? useCollaborativeEditing(props.pageId) : {
  connected: ref(false),
  users: ref([]),
  join: () => {},
  leave: () => {},
  broadcastUpdate: () => {},
  broadcastCursor: () => {},
  isReceivingUpdate: ref(false),
  pendingChanges: ref({})
}

// Refs
const editorRef = ref<HTMLElement>()

// Editor state
const activeFormats = ref<string[]>([])
const codeBlocks = ref<CodeBlockData[]>([])
const lastSavedContent = ref('')
const saveTimeout = ref<NodeJS.Timeout | null>(null)

// AI Assistant
const showAiPanel = ref(false)
const aiLoading = ref(false)
const aiResponse = ref('')
const aiPrompt = ref('')

// Block menu
const showBlockMenu = ref(false)
const blockMenuStyle = ref({})
const blockSearchQuery = ref('')

// Text formats
const textFormats = [
  { name: 'bold', icon: BoldIcon, title: 'Negrito (Ctrl+B)' },
  { name: 'italic', icon: ItalicIcon, title: 'Itálico (Ctrl+I)' },
  { name: 'underline', icon: UnderlineIcon, title: 'Sublinhado (Ctrl+U)' }
]

// Block types
const blockTypes = [
  { name: 'h1', icon: Heading1Icon, title: 'Título 1', description: 'Título principal' },
  { name: 'h2', icon: Heading2Icon, title: 'Título 2', description: 'Subtítulo' },
  { name: 'ul', icon: ListIcon, title: 'Lista', description: 'Lista com marcadores' },
  { name: 'ol', icon: ListIcon, title: 'Lista Numerada', description: 'Lista numerada' },
  { name: 'todo', icon: CheckSquareIcon, title: 'To-do', description: 'Lista de tarefas' },
  { name: 'quote', icon: QuoteIcon, title: 'Citação', description: 'Bloco de citação' },
  { name: 'divider', icon: DivideIcon, title: 'Divisor', description: 'Linha divisória' },
  { name: 'code', icon: TerminalIcon, title: 'Código', description: 'Bloco de código executável' },
  { name: 'math', icon: CalculatorIcon, title: 'Matemática', description: 'Fórmula matemática' },
  { name: 'image', icon: ImageIcon, title: 'Imagem', description: 'Inserir imagem' },
  { name: 'table', icon: TableIcon, title: 'Tabela', description: 'Tabela de dados' }
]

// Computed
const filteredBlockTypes = computed(() => {
  if (!blockSearchQuery.value) return blockTypes
  
  const query = blockSearchQuery.value.toLowerCase()
  return blockTypes.filter(block =>
    block.title.toLowerCase().includes(query) ||
    block.description.toLowerCase().includes(query)
  )
})

// Methods
function handleInput(event: Event) {
  if (isReceivingUpdate.value) return
  
  const target = event.target as HTMLElement
  const content = target.innerHTML
  
  emit('update', content)
  
  // Auto-save
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value)
  }
  
  saveTimeout.value = setTimeout(() => {
    if (content !== lastSavedContent.value) {
      emit('save', content)
      lastSavedContent.value = content
      
      if (props.pageId && connected.value) {
        broadcastUpdate({
          content,
          updated_at: new Date().toISOString()
        })
      }
    }
  }, 2000)
}

function handleKeydown(event: KeyboardEvent) {
  // Handle shortcuts
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        applyFormat('bold')
        break
      case 'i':
        event.preventDefault()
        applyFormat('italic')
        break
      case 'u':
        event.preventDefault()
        applyFormat('underline')
        break
      case 's':
        event.preventDefault()
        saveContent()
        break
    }
  }
  
  // Handle slash commands
  if (event.key === '/' && !event.ctrlKey && !event.metaKey) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()
      
      blockMenuStyle.value = {
        position: 'absolute',
        top: rect.bottom + 'px',
        left: rect.left + 'px',
        zIndex: 1000
      }
      
      showBlockMenu.value = true
      blockSearchQuery.value = ''
      
      nextTick(() => {
        const input = document.querySelector('.block-search-input') as HTMLInputElement
        if (input) input.focus()
      })
    }
  }
  
  // Hide block menu on escape
  if (event.key === 'Escape') {
    hideBlockMenu()
  }
}

function handleKeyup(event: KeyboardEvent) {
  updateActiveFormats()
  
  // Send cursor position for collaboration
  if (props.pageId && connected.value) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      broadcastCursor(range.startOffset)
    }
  }
}

function handleMouseUp() {
  updateActiveFormats()
}

function handleFocus() {
  updateActiveFormats()
  
  if (props.pageId) {
    join()
  }
}

function handleBlur() {
  saveContent()
  
  if (props.pageId) {
    leave()
  }
}

function handlePaste(event: ClipboardEvent) {
  event.preventDefault()
  
  const text = event.clipboardData?.getData('text/plain') || ''
  if (text) {
    document.execCommand('insertText', false, text)
  }
}

function applyFormat(format: string) {
  if (!editorRef.value || props.readonly) return
  
  editorRef.value.focus()
  
  switch (format) {
    case 'bold':
      document.execCommand('bold')
      break
    case 'italic':
      document.execCommand('italic')
      break
    case 'underline':
      document.execCommand('underline')
      break
  }
  
  updateActiveFormats()
  handleInput({ target: editorRef.value } as any)
}

function updateActiveFormats() {
  activeFormats.value = []
  
  if (document.queryCommandState('bold')) {
    activeFormats.value.push('bold')
  }
  if (document.queryCommandState('italic')) {
    activeFormats.value.push('italic')
  }
  if (document.queryCommandState('underline')) {
    activeFormats.value.push('underline')
  }
}

function insertBlock(type: string) {
  if (!editorRef.value || props.readonly) return
  
  hideBlockMenu()
  editorRef.value.focus()
  
  switch (type) {
    case 'h1':
      document.execCommand('formatBlock', false, 'h1')
      break
    case 'h2':
      document.execCommand('formatBlock', false, 'h2')
      break
    case 'ul':
      document.execCommand('insertUnorderedList')
      break
    case 'ol':
      document.execCommand('insertOrderedList')
      break
    case 'quote':
      document.execCommand('formatBlock', false, 'blockquote')
      break
    case 'divider':
      document.execCommand('insertHTML', false, '<hr>')
      break
    case 'code':
      insertCodeBlock()
      break
    case 'math':
      insertMathBlock()
      break
    case 'todo':
      insertTodoBlock()
      break
  }
  
  handleInput({ target: editorRef.value } as any)
}

function insertCodeBlock() {
  const blockId = `code_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  const position = getCaretPosition()
  
  codeBlocks.value.push({
    id: blockId,
    content: '# Seu código aqui\nprint("Hello, World!")',
    language: 'python',
    position
  })
}

function insertMathBlock() {
  const mathExpression = prompt('Digite a expressão matemática (LaTeX):')
  if (mathExpression && editorRef.value) {
    const mathHtml = `<div class="math-block" contenteditable="false">$$${mathExpression}$$</div><br>`
    document.execCommand('insertHTML', false, mathHtml)
  }
}

function insertTodoBlock() {
  const todoHtml = '<div class="todo-item"><input type="checkbox"> <span contenteditable="true">Nova tarefa</span></div><br>'
  document.execCommand('insertHTML', false, todoHtml)
}

function hideBlockMenu() {
  showBlockMenu.value = false
  blockSearchQuery.value = ''
}

function updateCodeBlock(blockId: string, newCode: string) {
  const block = codeBlocks.value.find(b => b.id === blockId)
  if (block) {
    block.content = newCode
    handleInput({ target: editorRef.value } as any)
  }
}

function updateCodeBlockLanguage(blockId: string, newLanguage: string) {
  const block = codeBlocks.value.find(b => b.id === blockId)
  if (block) {
    block.language = newLanguage
    handleInput({ target: editorRef.value } as any)
  }
}

function removeCodeBlock(blockId: string) {
  codeBlocks.value = codeBlocks.value.filter(b => b.id !== blockId)
  handleInput({ target: editorRef.value } as any)
}

function onCodeExecute(blockId: string, data: { code: string; language: string }) {
  emit('code-execute', blockId, data.code, data.language)
}

function getCaretPosition(): number {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return 0
  
  const range = selection.getRangeAt(0)
  return range.startOffset
}

function saveContent() {
  if (!editorRef.value) return
  
  const content = editorRef.value.innerHTML
  if (content !== lastSavedContent.value) {
    emit('save', content)
    lastSavedContent.value = content
  }
}

// AI Assistant methods
function toggleAiAssistant() {
  showAiPanel.value = !showAiPanel.value
}

async function improveText() {
  const selectedText = getSelectedText()
  if (!selectedText) return
  
  await callAiAssistant(`Melhore este texto: "${selectedText}"`)
}

async function summarizeText() {
  const selectedText = getSelectedText() || editorRef.value?.innerText || ''
  if (!selectedText) return
  
  await callAiAssistant(`Resuma este texto: "${selectedText}"`)
}

async function translateText() {
  const selectedText = getSelectedText()
  if (!selectedText) return
  
  const targetLang = prompt('Traduzir para qual idioma?', 'inglês')
  if (targetLang) {
    await callAiAssistant(`Traduza este texto para ${targetLang}: "${selectedText}"`)
  }
}

async function continueWriting() {
  const content = editorRef.value?.innerText || ''
  const lastParagraph = content.split('\n').pop() || content.slice(-200)
  
  await callAiAssistant(`Continue escrevendo este texto de forma natural: "${lastParagraph}"`)
}

async function customAiAction() {
  const selectedText = getSelectedText() || editorRef.value?.innerText || ''
  const prompt = aiPrompt.value
  
  await callAiAssistant(`${prompt}\n\nTexto: "${selectedText}"`)
}

async function callAiAssistant(prompt: string) {
  aiLoading.value = true
  aiResponse.value = ''
  
  try {
    const response = await fetch('http://localhost:5001/api/llm/complete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt,
        provider: 'openrouter',
        model: 'anthropic/claude-3-sonnet',
        max_tokens: 500
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      aiResponse.value = result.response
    } else {
      throw new Error('Falha na requisição')
    }
  } catch (error) {
    aiResponse.value = 'Erro ao conectar com o assistente IA'
  } finally {
    aiLoading.value = false
  }
}

function getSelectedText(): string {
  const selection = window.getSelection()
  return selection?.toString() || ''
}

function applyAiSuggestion() {
  if (aiResponse.value && editorRef.value) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.deleteContents()
      range.insertNode(document.createTextNode(aiResponse.value))
    } else {
      // Insert at the end
      editorRef.value.innerHTML += `<p>${aiResponse.value}</p>`
    }
    
    handleInput({ target: editorRef.value } as any)
    dismissAiSuggestion()
  }
}

function dismissAiSuggestion() {
  aiResponse.value = ''
  aiPrompt.value = ''
}

function formatAiResponse(response: string): string {
  return response
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>')
}

// Watch for content changes
watch(() => props.content, (newContent) => {
  if (newContent && editorRef.value && editorRef.value.innerHTML !== newContent) {
    editorRef.value.innerHTML = newContent
    lastSavedContent.value = newContent
  }
}, { immediate: true })

// Watch for collaborative changes
watch(pendingChanges, (changes) => {
  if (changes.content && editorRef.value && !isReceivingUpdate.value) {
    editorRef.value.innerHTML = changes.content
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (props.content && editorRef.value) {
    editorRef.value.innerHTML = props.content
    lastSavedContent.value = props.content
  }
})

onUnmounted(() => {
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value)
  }
  
  if (props.pageId) {
    leave()
  }
})
</script>

<style scoped>
.enhanced-editor {
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  background: white;
  overflow: hidden;
  position: relative;
}

/* Toolbar */
.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #e9e9e7;
  background: #f7f6f3;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background: #e9e9e7;
  margin: 0 4px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
}

.toolbar-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.toolbar-btn.active {
  background: #2383e2;
  color: white;
}

.toolbar-btn.ai-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  color: white;
}

.toolbar-btn.connected {
  background: #22c55e;
  color: white;
}

.collaborator-count {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-icon {
  width: 16px;
  height: 16px;
}

/* Collaborators */
.collaborators-cursors {
  position: absolute;
  pointer-events: none;
  z-index: 100;
}

.cursor {
  position: absolute;
  transition: all 0.1s ease;
}

.cursor-line {
  width: 2px;
  height: 20px;
}

.cursor-label {
  position: absolute;
  top: -6px;
  left: 4px;
  padding: 2px 6px;
  font-size: 11px;
  color: white;
  border-radius: 3px;
  white-space: nowrap;
}

/* Editor */
.editor-container {
  min-height: 400px;
  position: relative;
}

.editor-container.readonly {
  background: #f9f9f9;
}

.editor-content {
  padding: 24px;
  min-height: 376px;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #37352f;
}

.editor-content:empty::before {
  content: attr(placeholder);
  color: #c9c7c4;
  pointer-events: none;
}

/* Block styles */
.editor-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1rem 0;
}

.editor-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0.8rem 0;
}

.editor-content blockquote {
  border-left: 4px solid #e9e9e7;
  padding-left: 16px;
  margin: 16px 0;
  color: #787774;
  font-style: italic;
}

.editor-content .todo-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}

.editor-content .math-block {
  background: #f7f6f3;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  padding: 12px;
  margin: 16px 0;
  font-family: 'Times New Roman', serif;
  text-align: center;
}

/* Dynamic code blocks */
.dynamic-code-block {
  position: relative;
  margin: 16px 0;
}

.remove-block-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(224, 62, 62, 0.9);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.15s ease;
  z-index: 10;
}

.dynamic-code-block:hover .remove-block-btn {
  opacity: 1;
}

.remove-icon {
  width: 14px;
  height: 14px;
}

/* AI Assistant Panel */
.ai-assistant-panel {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 350px;
  max-height: 80vh;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.ai-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  color: white;
}

.ai-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.ai-icon {
  width: 18px;
  height: 18px;
}

.close-ai-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-ai-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.close-icon {
  width: 16px;
  height: 16px;
}

.ai-panel-content {
  padding: 20px;
  max-height: calc(80vh - 70px);
  overflow-y: auto;
}

.ai-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 20px;
}

.ai-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  border: 1px solid #e9e9e7;
  background: white;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.ai-action-btn:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #8b5cf6;
}

.ai-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.ai-action-icon {
  width: 16px;
  height: 16px;
}

.ai-input-group {
  margin-bottom: 20px;
}

.ai-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  margin-bottom: 8px;
}

.ai-submit-btn {
  width: 100%;
  padding: 10px;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.ai-submit-btn:hover:not(:disabled) {
  background: #7c3aed;
}

.ai-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.ai-response {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.ai-response-header {
  padding: 12px 16px;
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 500;
  font-size: 14px;
}

.ai-response-content {
  padding: 16px;
  font-size: 14px;
  line-height: 1.5;
}

.ai-response-actions {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.apply-btn, .dismiss-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.apply-btn {
  background: #22c55e;
  color: white;
  border-color: #22c55e;
}

.apply-btn:hover {
  background: #16a34a;
}

.dismiss-btn {
  background: white;
  color: #6b7280;
}

.dismiss-btn:hover {
  background: #f9fafb;
}

.ai-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  color: #6b7280;
  justify-content: center;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Block menu */
.block-menu {
  position: absolute;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  min-width: 280px;
  max-height: 300px;
  overflow: hidden;
  z-index: 1000;
}

.block-menu-search {
  padding: 12px;
  border-bottom: 1px solid #f1f1ef;
}

.block-search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  outline: none;
  font-size: 14px;
}

.block-menu-items {
  max-height: 240px;
  overflow-y: auto;
}

.block-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.block-menu-item:hover {
  background: #f7f6f3;
}

.block-menu-icon {
  width: 20px;
  height: 20px;
  color: #787774;
  flex-shrink: 0;
}

.block-menu-title {
  font-weight: 500;
  color: #37352f;
  margin-bottom: 2px;
}

.block-menu-description {
  font-size: 13px;
  color: #9b9a97;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .ai-assistant-panel {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    max-height: none;
    transform: none;
    border-radius: 0;
  }
  
  .editor-toolbar {
    flex-wrap: wrap;
    padding: 8px;
  }
  
  .toolbar-group {
    flex-wrap: wrap;
  }
}
</style>
