<template>
  <div class="code-block">
    <!-- Code Header -->
    <div class="code-header">
      <div class="header-left">
        <select v-model="language" @change="updateLanguage" class="language-select">
          <option value="python">🐍 Python</option>
          <option value="javascript">🟨 JavaScript</option>
          <option value="typescript">🔷 TypeScript</option>
          <option value="bash">🐚 Bash</option>
        </select>
      </div>
      
      <div class="header-right">
        <button @click="copyCode" class="header-btn" title="Copiar código">
          📋
        </button>
        <button 
          v-if="language === 'python'"
          @click="executeCode" 
          :disabled="isExecuting || !codeContent.trim()"
          class="execute-btn"
          title="Executar código"
        >
          {{ isExecuting ? '⏳ Executando...' : '▶️ Executar' }}
        </button>
      </div>
    </div>
    
    <!-- Code Editor -->
    <div class="code-editor">
      <textarea
        v-model="codeContent"
        @input="updateCode"
        @keydown="handleKeyDown"
        :placeholder="getPlaceholder()"
        class="code-textarea"
        spellcheck="false"
      ></textarea>
      
      <!-- Line Numbers (Simple) -->
      <div class="line-numbers">
        <div v-for="n in lineCount" :key="n" class="line-number">{{ n }}</div>
      </div>
    </div>
    
    <!-- Execution Output -->
    <div v-if="executionOutput" class="code-output" :class="outputClass">
      <div class="output-header">
        <span class="output-title">
          {{ executionSuccess ? '✅ Resultado' : '❌ Erro' }}
        </span>
        <button @click="clearOutput" class="clear-btn" title="Limpar resultado">
          🗑️
        </button>
      </div>
      <pre class="output-content">{{ executionOutput }}</pre>
      <div class="output-actions">
        <button @click="copyOutput" class="output-btn">📋 Copiar resultado</button>
      </div>
    </div>
    
    <!-- AI Assistant (Simplified) -->
    <div v-if="showAI" class="ai-assistant">
      <div class="ai-header">
        <span>🤖 Assistente IA</span>
        <button @click="showAI = false" class="close-ai">❌</button>
      </div>
      <div class="ai-actions">
        <button @click="aiAction('explain')" :disabled="isAIWorking" class="ai-btn">
          💭 Explicar
        </button>
        <button @click="aiAction('optimize')" :disabled="isAIWorking" class="ai-btn">
          ⚡ Otimizar
        </button>
        <button @click="aiAction('fix')" :disabled="isAIWorking" class="ai-btn">
          🔧 Corrigir
        </button>
      </div>
      
      <div v-if="aiResponse" class="ai-response">
        <div class="response-header">{{ aiResponse.title }}</div>
        <div class="response-content">
          <pre v-if="aiResponse.type === 'code'" class="response-code">{{ aiResponse.content }}</pre>
          <p v-else class="response-text">{{ aiResponse.content }}</p>
        </div>
        <div v-if="aiResponse.type === 'code'" class="response-actions">
          <button @click="applyAISuggestion" class="apply-btn">✅ Aplicar</button>
          <button @click="aiResponse = null" class="discard-btn">❌ Descartar</button>
        </div>
      </div>
      
      <div v-if="isAIWorking" class="ai-loading">
        <span>🤖 IA processando...</span>
      </div>
    </div>
    
    <!-- Code Actions -->
    <div class="code-actions">
      <button @click="showAI = !showAI" class="action-btn" :class="{ active: showAI }">
        🤖 IA
      </button>
      <button @click="formatCode" class="action-btn">
        🎨 Formatar
      </button>
      <button @click="removeBlock" class="action-btn danger">
        🗑️ Remover
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface CodeBlock {
  id: string
  type: 'code'
  content: {
    code: string
    language: string
    output?: string
  }
}

interface Props {
  block: CodeBlock
}

interface Emits {
  update: [block: CodeBlock]
  delete: [id: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const codeContent = ref(props.block.content.code || '')
const language = ref(props.block.content.language || 'python')
const isExecuting = ref(false)
const executionOutput = ref('')
const executionSuccess = ref(true)
const showAI = ref(false)
const isAIWorking = ref(false)
const aiResponse = ref<{ title: string, content: string, type: 'code' | 'text' } | null>(null)

// Computed
const lineCount = computed(() => {
  return Math.max(codeContent.value.split('\n').length, 5)
})

const outputClass = computed(() => ({
  'output-success': executionSuccess.value,
  'output-error': !executionSuccess.value
}))

// Methods
function updateCode() {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      code: codeContent.value
    }
  })
}

function updateLanguage() {
  emit('update', {
    ...props.block,
    content: {
      ...props.block.content,
      language: language.value
    }
  })
}

function handleKeyDown(event: KeyboardEvent) {
  // Tab handling
  if (event.key === 'Tab') {
    event.preventDefault()
    const textarea = event.target as HTMLTextAreaElement
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    
    const spaces = '  ' // 2 spaces
    codeContent.value = codeContent.value.substring(0, start) + spaces + codeContent.value.substring(end)
    
    setTimeout(() => {
      textarea.selectionStart = textarea.selectionEnd = start + spaces.length
    })
  }
}

async function executeCode() {
  if (!codeContent.value.trim() || language.value !== 'python') return
  
  isExecuting.value = true
  executionOutput.value = ''
  
  try {
    // Simulate code execution (in real app, call backend API)
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock execution result
    if (codeContent.value.includes('print')) {
      executionSuccess.value = true
      executionOutput.value = `Resultado da execução:\n${codeContent.value.includes('error') ? 'Erro simulado!' : 'Hello, World!\nExecução concluída com sucesso.'}`
    } else {
      executionSuccess.value = false
      executionOutput.value = 'NameError: name "undefined_variable" is not defined'
    }
    
  } catch (error) {
    executionSuccess.value = false
    executionOutput.value = `Erro de execução: ${error}`
  } finally {
    isExecuting.value = false
  }
}

async function aiAction(action: string) {
  if (!codeContent.value.trim()) return
  
  isAIWorking.value = true
  aiResponse.value = null
  
  try {
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const responses = {
      explain: {
        title: '💭 Explicação do Código',
        type: 'text' as const,
        content: 'Este código utiliza a função print() do Python para exibir uma mensagem na tela. É uma operação básica de saída de dados muito comum em programação.'
      },
      optimize: {
        title: '⚡ Versão Otimizada',
        type: 'code' as const,
        content: `# Versão otimizada\n${codeContent.value}\n\n# Melhorias aplicadas:\n# - Adicionado comentários\n# - Estrutura mais limpa`
      },
      fix: {
        title: '🔧 Código Corrigido',
        type: 'code' as const,
        content: codeContent.value.replace(/print\(/g, 'print(').replace(/;$/gm, '')
      }
    }
    
    aiResponse.value = responses[action as keyof typeof responses] || responses.explain
    
  } catch (error) {
    console.error('AI Error:', error)
  } finally {
    isAIWorking.value = false
  }
}

function applyAISuggestion() {
  if (aiResponse.value && aiResponse.value.type === 'code') {
    codeContent.value = aiResponse.value.content
    updateCode()
    aiResponse.value = null
  }
}

function copyCode() {
  navigator.clipboard.writeText(codeContent.value).then(() => {
    alert('Código copiado! 📋')
  })
}

function copyOutput() {
  if (executionOutput.value) {
    navigator.clipboard.writeText(executionOutput.value)
    alert('Resultado copiado! 📋')
  }
}

function clearOutput() {
  executionOutput.value = ''
}

function formatCode() {
  // Simple code formatting
  const formatted = codeContent.value
    .split('\n')
    .map(line => line.trim())
    .join('\n')
    .replace(/;+$/gm, '')
  
  codeContent.value = formatted
  updateCode()
  alert('Código formatado! 🎨')
}

function removeBlock() {
  emit('delete', props.block.id)
}

function getPlaceholder(): string {
  const placeholders = {
    python: '# Digite seu código Python aqui...\nprint("Hello, World!")',
    javascript: '// Digite seu código JavaScript aqui...\nconsole.log("Hello, World!");',
    typescript: '// Digite seu código TypeScript aqui...\nconst message: string = "Hello, World!";',
    bash: '# Digite seus comandos bash aqui...\necho "Hello, World!"'
  }
  return placeholders[language.value as keyof typeof placeholders] || 'Digite seu código aqui...'
}

// Sync with external changes
watch(() => props.block.content, (newContent) => {
  codeContent.value = newContent.code || ''
  language.value = newContent.language || 'python'
}, { immediate: true })
</script>

<style scoped>
.code-block {
  margin: 15px 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Code Header */
.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-select {
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  font-size: 0.85rem;
  cursor: pointer;
}

.header-btn {
  background: none;
  border: none;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.header-btn:hover {
  background: rgba(0,0,0,0.1);
}

.execute-btn {
  background: #48bb78;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.85rem;
  cursor: pointer;
}

.execute-btn:hover:not(:disabled) {
  background: #38a169;
}

.execute-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

/* Code Editor */
.code-editor {
  position: relative;
  display: flex;
  min-height: 120px;
}

.line-numbers {
  background: #f7fafc;
  padding: 15px 8px;
  border-right: 1px solid #e2e8f0;
  min-width: 40px;
  text-align: right;
  font-size: 0.8rem;
  color: #a0aec0;
  user-select: none;
  line-height: 1.5;
}

.code-textarea {
  flex: 1;
  padding: 15px;
  border: none;
  outline: none;
  background: transparent;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #2d3748;
  resize: vertical;
  min-height: 120px;
}

/* Code Output */
.code-output {
  border-top: 1px solid #e2e8f0;
}

.output-success {
  background: rgba(72, 187, 120, 0.05);
  border-top-color: rgba(72, 187, 120, 0.2);
}

.output-error {
  background: rgba(229, 62, 62, 0.05);
  border-top-color: rgba(229, 62, 62, 0.2);
}

.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background: rgba(255,255,255,0.7);
  border-bottom: 1px solid #e2e8f0;
}

.output-title {
  font-size: 0.85rem;
  font-weight: 600;
}

.clear-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 2px;
}

.output-content {
  margin: 0;
  padding: 12px 15px;
  font-family: inherit;
  font-size: 0.85rem;
  line-height: 1.4;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.output-success .output-content {
  color: #2f855a;
}

.output-error .output-content {
  color: #e53e3e;
}

.output-actions {
  padding: 8px 15px;
  border-top: 1px solid #e2e8f0;
}

.output-btn {
  background: white;
  border: 1px solid #e2e8f0;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.output-btn:hover {
  background: #f7fafc;
}

/* AI Assistant */
.ai-assistant {
  border-top: 1px solid #e2e8f0;
  background: #fafafa;
}

.ai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 0.9rem;
}

.close-ai {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.ai-actions {
  display: flex;
  gap: 8px;
  padding: 12px 15px;
  flex-wrap: wrap;
}

.ai-btn {
  background: white;
  border: 1px solid #e2e8f0;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.ai-btn:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #d1d5db;
}

.ai-btn:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

.ai-response {
  margin: 12px 15px;
  padding: 12px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.response-header {
  font-weight: 600;
  font-size: 0.85rem;
  color: #4299e1;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.response-code {
  background: #f7fafc;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  margin: 0;
}

.response-text {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #4a5568;
  margin: 0;
}

.response-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.apply-btn {
  background: #48bb78;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.discard-btn {
  background: #e2e8f0;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.ai-loading {
  padding: 15px;
  text-align: center;
  color: #718096;
  font-size: 0.9rem;
}

/* Code Actions */
.code-actions {
  display: flex;
  gap: 8px;
  padding: 10px 15px;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

.action-btn {
  background: white;
  border: 1px solid #e2e8f0;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.action-btn:hover {
  background: #edf2f7;
}

.action-btn.active {
  background: #4299e1;
  color: white;
  border-color: #4299e1;
}

.action-btn.danger:hover {
  background: #fed7d7;
  color: #e53e3e;
}
</style>
