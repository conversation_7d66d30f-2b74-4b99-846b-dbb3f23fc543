<template>
  <div class="block-menu-simple">
    <!-- Slot para o trigger customizado -->
    <div @click="toggleMenu">
      <slot name="trigger" :toggle="toggleMenu">
        <button class="default-trigger">
          Adicionar <PERSON>
        </button>
      </slot>
    </div>
    
    <!-- Menu simples -->
    <div v-if="isOpen" class="menu">
      <div class="menu-item" @click="selectBlock('text')">📝 Texto</div>
      <div class="menu-item" @click="selectBlock('image')">🖼️ Imagem</div>
      <div class="menu-item" @click="selectBlock('link')">🔗 Link</div>
      <div class="menu-item" @click="selectBlock('code')">💻 Código</div>
    </div>
    
    <!-- Overlay para fechar -->
    <div v-if="isOpen" class="overlay" @click="closeMenu"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Emits {
  selectBlock: [type: string]
}

const emit = defineEmits<Emits>()

const isOpen = ref(false)

function toggleMenu() {
  isOpen.value = !isOpen.value
}

function closeMenu() {
  isOpen.value = false
}

function selectBlock(type: string) {
  emit('selectBlock', type)
  closeMenu()
}
</script>

<style scoped>
.block-menu-simple {
  position: relative;
  display: inline-block;
}

.default-trigger {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1000;
  min-width: 200px;
}

.menu-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.menu-item:hover {
  background: #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
</style>
