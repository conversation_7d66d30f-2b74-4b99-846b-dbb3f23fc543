<template>
  <div class="block-menu">
    <!-- Trigger <PERSON>ton -->
    <button
      ref="triggerButton"
      @click="toggleMenu"
      class="menu-trigger"
      :class="{ active: isMenuOpen }"
      title="Adicionar bloco"
    >
      <PlusIcon class="trigger-icon" />
    </button>

    <!-- Menu Dropdown -->
    <Teleport to="body">
      <div
        v-if="isMenuOpen"
        ref="menuDropdown"
        class="menu-dropdown"
        :style="menuPosition"
        @click.stop
      >
        <div class="menu-header">
          <span>Adicionar bloco</span>
        </div>
        
        <div class="menu-section">
          <div class="section-title">Básicos</div>
          <button
            v-for="basicBlock in basicBlocks"
            :key="basicBlock.type"
            @click="selectBlockType(basicBlock.type)"
            class="menu-item"
          >
            <component :is="basicBlock.icon" class="menu-item-icon" />
            <div class="menu-item-content">
              <div class="menu-item-title">{{ basicBlock.title }}</div>
              <div class="menu-item-desc">{{ basicBlock.description }}</div>
            </div>
          </button>
        </div>

        <div class="menu-section">
          <div class="section-title">Mídia</div>
          <button
            v-for="mediaBlock in mediaBlocks"
            :key="mediaBlock.type"
            @click="selectBlockType(mediaBlock.type)"
            class="menu-item"
          >
            <component :is="mediaBlock.icon" class="menu-item-icon" />
            <div class="menu-item-content">
              <div class="menu-item-title">{{ mediaBlock.title }}</div>
              <div class="menu-item-desc">{{ mediaBlock.description }}</div>
            </div>
          </button>
        </div>

        <div class="menu-section">
          <div class="section-title">Avançados</div>
          <button
            v-for="advancedBlock in advancedBlocks"
            :key="advancedBlock.type"
            @click="selectBlockType(advancedBlock.type)"
            class="menu-item"
          >
            <component :is="advancedBlock.icon" class="menu-item-icon" />
            <div class="menu-item-content">
              <div class="menu-item-title">{{ advancedBlock.title }}</div>
              <div class="menu-item-desc">{{ advancedBlock.description }}</div>
            </div>
          </button>
        </div>
      </div>
    </Teleport>

    <!-- Overlay -->
    <div
      v-if="isMenuOpen"
      class="menu-overlay"
      @click="closeMenu"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import {
  Plus as PlusIcon,
  Type as TextIcon,
  Heading1,
  Heading2,
  List,
  Image as ImageIcon,
  Link as LinkIcon,
  Code as CodeIcon,
  Calculator as MathIcon,
  Quote,
  Separator as DividerIcon
} from 'lucide-vue-next'

export interface BlockType {
  type: string
  title: string
  description: string
  icon: any
}

interface Emits {
  selectBlock: [type: string]
}

const emit = defineEmits<Emits>()

// Refs
const triggerButton = ref<HTMLElement | null>(null)
const menuDropdown = ref<HTMLElement | null>(null)
const isMenuOpen = ref(false)
const menuPosition = ref({ top: '0px', left: '0px' })

// Block types
const basicBlocks: BlockType[] = [
  {
    type: 'text',
    title: 'Texto',
    description: 'Parágrafo simples',
    icon: TextIcon
  },
  {
    type: 'heading1',
    title: 'Título 1',
    description: 'Título principal',
    icon: Heading1
  },
  {
    type: 'heading2',
    title: 'Título 2',
    description: 'Subtítulo',
    icon: Heading2
  },
  {
    type: 'bulleted-list',
    title: 'Lista',
    description: 'Lista com marcadores',
    icon: List
  },
  {
    type: 'quote',
    title: 'Citação',
    description: 'Destacar uma citação',
    icon: Quote
  },
  {
    type: 'divider',
    title: 'Divisor',
    description: 'Linha divisória',
    icon: DividerIcon
  }
]

const mediaBlocks: BlockType[] = [
  {
    type: 'image',
    title: 'Imagem',
    description: 'Upload ou link de imagem',
    icon: ImageIcon
  },
  {
    type: 'link',
    title: 'Link',
    description: 'Link com preview',
    icon: LinkIcon
  }
]

const advancedBlocks: BlockType[] = [
  {
    type: 'code',
    title: 'Código Python',
    description: 'Código executável',
    icon: CodeIcon
  },
  {
    type: 'math',
    title: 'Fórmula',
    description: 'Expressão matemática',
    icon: MathIcon
  }
]

// Methods
function toggleMenu() {
  if (isMenuOpen.value) {
    closeMenu()
  } else {
    openMenu()
  }
}

async function openMenu() {
  isMenuOpen.value = true
  await nextTick()
  calculateMenuPosition()
}

function closeMenu() {
  isMenuOpen.value = false
}

function calculateMenuPosition() {
  if (!triggerButton.value || !menuDropdown.value) return

  const buttonRect = triggerButton.value.getBoundingClientRect()
  const menuRect = menuDropdown.value.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  // Default position (to the right of button)
  let top = buttonRect.top
  let left = buttonRect.right + 8

  // Adjust if menu would go off screen
  if (left + menuRect.width > viewport.width) {
    left = buttonRect.left - menuRect.width - 8
  }

  if (top + menuRect.height > viewport.height) {
    top = viewport.height - menuRect.height - 16
  }

  if (top < 16) {
    top = 16
  }

  menuPosition.value = {
    top: `${Math.max(0, top)}px`,
    left: `${Math.max(0, left)}px`
  }
}

function selectBlockType(type: string) {
  emit('selectBlock', type)
  closeMenu()
}

// Handle clicks outside
function handleClickOutside(event: MouseEvent) {
  const target = event.target as Element
  if (!triggerButton.value?.contains(target) && !menuDropdown.value?.contains(target)) {
    closeMenu()
  }
}

// Handle escape key
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Escape' && isMenuOpen.value) {
    closeMenu()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeyDown)
  window.addEventListener('resize', calculateMenuPosition)
  window.addEventListener('scroll', calculateMenuPosition)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('resize', calculateMenuPosition)
  window.removeEventListener('scroll', calculateMenuPosition)
})
</script>

<style scoped>
.block-menu {
  position: relative;
  display: inline-block;
}

.menu-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: #f7f6f3;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid transparent;
}

.menu-trigger:hover {
  background: #e9e9e7;
  border-color: #d3d1cb;
}

.menu-trigger.active {
  background: #2383e2;
  border-color: #2383e2;
}

.menu-trigger.active .trigger-icon {
  color: white;
}

.trigger-icon {
  width: 18px;
  height: 18px;
  color: #787774;
  transition: color 0.15s ease;
}

.menu-dropdown {
  position: fixed;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 12px;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 280px;
  max-width: 320px;
  max-height: 400px;
  overflow-y: auto;
  padding: 0;
}

.menu-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f1ef;
  font-size: 14px;
  font-weight: 600;
  color: #37352f;
  background: #fafafa;
  border-radius: 12px 12px 0 0;
}

.menu-section {
  padding: 8px 0;
}

.section-title {
  padding: 8px 16px 4px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #9b9a97;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.menu-item:hover {
  background: rgba(55, 53, 47, 0.08);
}

.menu-item-icon {
  width: 18px;
  height: 18px;
  color: #787774;
  flex-shrink: 0;
}

.menu-item-content {
  flex: 1;
  min-width: 0;
}

.menu-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #37352f;
  margin-bottom: 2px;
}

.menu-item-desc {
  font-size: 12px;
  color: #787774;
  line-height: 1.3;
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Scrollbar styling */
.menu-dropdown::-webkit-scrollbar {
  width: 6px;
}

.menu-dropdown::-webkit-scrollbar-track {
  background: transparent;
}

.menu-dropdown::-webkit-scrollbar-thumb {
  background: #d3d1cb;
  border-radius: 3px;
}

.menu-dropdown::-webkit-scrollbar-thumb:hover {
  background: #b8b6b0;
}
</style>
