<template>
  <div class="blocknote-editor">
    <div class="editor-toolbar">
      <div class="toolbar-group">
        <button
          v-for="format in formats"
          :key="format.name"
          @click="applyFormat(format)"
          :class="{ active: activeFormats.includes(format.name) }"
          class="toolbar-btn"
          :title="format.title"
        >
          <component :is="format.icon" class="toolbar-icon" />
        </button>
      </div>
      
      <div class="toolbar-group">
        <button @click="addCodeBlock" class="toolbar-btn" title="Código Executável">
          <CodeIcon class="toolbar-icon" />
        </button>
        <button @click="addMathBlock" class="toolbar-btn" title="Bloco de Matemática">
          <CalculatorIcon class="toolbar-icon" />
        </button>
      </div>
    </div>
    
    <div class="editor-container">
      <div
        ref="editorRef"
        class="editor-content"
        contenteditable="true"
        @input="handleInput"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
        placeholder="Comece a escrever..."
      ></div>
    </div>
    
    <!-- Code Block Modal -->
    <div v-if="showCodeModal" class="modal-overlay" @click="closeCodeModal">
      <div class="code-modal" @click.stop>
        <h3>Adicionar Código Executável</h3>
        <select v-model="codeLanguage" class="language-select">
          <option value="python">Python</option>
          <option value="javascript">JavaScript</option>
          <option value="typescript">TypeScript</option>
        </select>
        <textarea
          v-model="codeContent"
          class="code-textarea"
          placeholder="Digite seu código aqui..."
        ></textarea>
        <div class="modal-actions">
          <button @click="insertCodeBlock" class="btn-primary">Inserir</button>
          <button @click="closeCodeModal" class="btn-secondary">Cancelar</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import {
  Bold,
  Italic,
  Underline,
  Code as CodeIcon,
  Calculator as CalculatorIcon,
  Heading1,
  Heading2,
  List
} from 'lucide-vue-next'

interface Props {
  content?: string
}

interface Emits {
  update: [content: string]
  save: [content: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const editorRef = ref<HTMLElement | null>(null)
const showCodeModal = ref(false)
const codeLanguage = ref('python')
const codeContent = ref('')

// Editor state
const activeFormats = ref<string[]>([])
const lastSavedContent = ref('')

// Toolbar formats
const formats = [
  { name: 'bold', icon: Bold, title: 'Negrito' },
  { name: 'italic', icon: Italic, title: 'Itálico' },
  { name: 'underline', icon: Underline, title: 'Sublinhado' },
  { name: 'h1', icon: Heading1, title: 'Título 1' },
  { name: 'h2', icon: Heading2, title: 'Título 2' },
  { name: 'ul', icon: List, title: 'Lista' }
]

// Methods
function handleInput() {
  if (!editorRef.value) return
  
  const content = editorRef.value.innerHTML
  emit('update', content)
  
  // Auto-save after 2 seconds of inactivity
  clearTimeout(saveTimeout.value)
  saveTimeout.value = setTimeout(() => {
    if (content !== lastSavedContent.value) {
      emit('save', content)
      lastSavedContent.value = content
    }
  }, 2000)
}

const saveTimeout = ref<NodeJS.Timeout | null>(null)

function handleKeydown(event: KeyboardEvent) {
  // Handle keyboard shortcuts
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        applyFormat({ name: 'bold', icon: Bold, title: 'Bold' })
        break
      case 'i':
        event.preventDefault()
        applyFormat({ name: 'italic', icon: Italic, title: 'Italic' })
        break
      case 's':
        event.preventDefault()
        emit('save', editorRef.value?.innerHTML || '')
        break
    }
  }
  
  // Handle Enter key for new blocks
  if (event.key === 'Enter' && !event.shiftKey) {
    // Add logic for creating new blocks
  }
}

function handleFocus() {
  updateActiveFormats()
}

function handleBlur() {
  // Save on blur
  if (editorRef.value) {
    const content = editorRef.value.innerHTML
    if (content !== lastSavedContent.value) {
      emit('save', content)
      lastSavedContent.value = content
    }
  }
}

function applyFormat(format: any) {
  if (!editorRef.value) return
  
  editorRef.value.focus()
  
  switch (format.name) {
    case 'bold':
      document.execCommand('bold')
      break
    case 'italic':
      document.execCommand('italic')
      break
    case 'underline':
      document.execCommand('underline')
      break
    case 'h1':
      document.execCommand('formatBlock', false, 'h1')
      break
    case 'h2':
      document.execCommand('formatBlock', false, 'h2')
      break
    case 'ul':
      document.execCommand('insertUnorderedList')
      break
  }
  
  updateActiveFormats()
  handleInput()
}

function updateActiveFormats() {
  activeFormats.value = []
  
  if (document.queryCommandState('bold')) {
    activeFormats.value.push('bold')
  }
  if (document.queryCommandState('italic')) {
    activeFormats.value.push('italic')
  }
  if (document.queryCommandState('underline')) {
    activeFormats.value.push('underline')
  }
}

function addCodeBlock() {
  showCodeModal.value = true
  codeContent.value = ''
}

function addMathBlock() {
  // TODO: Implement math block
  const mathContent = prompt('Digite a expressão matemática (LaTeX):')
  if (mathContent && editorRef.value) {
    const mathElement = `<div class="math-block" contenteditable="false">${mathContent}</div>`
    document.execCommand('insertHTML', false, mathElement)
    handleInput()
  }
}

function insertCodeBlock() {
  if (!editorRef.value || !codeContent.value.trim()) return
  
  const codeBlock = `
    <div class="code-block" contenteditable="false">
      <div class="code-header">
        <span class="code-language">${codeLanguage.value}</span>
        <button class="execute-btn" onclick="executeCode(this)">▶ Executar</button>
      </div>
      <pre class="code-content"><code>${codeContent.value}</code></pre>
      <div class="code-output" style="display: none;"></div>
    </div>
    <div><br></div>
  `
  
  document.execCommand('insertHTML', false, codeBlock)
  closeCodeModal()
  handleInput()
}

function closeCodeModal() {
  showCodeModal.value = false
  codeContent.value = ''
}

// Watchers
watch(() => props.content, (newContent) => {
  if (newContent && editorRef.value && editorRef.value.innerHTML !== newContent) {
    editorRef.value.innerHTML = newContent
    lastSavedContent.value = newContent
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  if (props.content && editorRef.value) {
    editorRef.value.innerHTML = props.content
    lastSavedContent.value = props.content
  }
  
  // Add global function for code execution
  ;(window as any).executeCode = async (button: HTMLElement) => {
    const codeBlock = button.closest('.code-block')
    if (!codeBlock) return
    
    const codeContent = codeBlock.querySelector('.code-content code')?.textContent
    const outputDiv = codeBlock.querySelector('.code-output') as HTMLElement
    const language = codeBlock.querySelector('.code-language')?.textContent
    
    if (!codeContent || !outputDiv) return
    
    // Show output div
    outputDiv.style.display = 'block'
    outputDiv.innerHTML = '<div class="loading">Executando...</div>'
    
    try {
      const response = await fetch('http://localhost:5000/api/code/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code: codeContent,
          language: language || 'python'
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        outputDiv.innerHTML = `<pre class="output-success">${result.output}</pre>`
      } else {
        outputDiv.innerHTML = `<pre class="output-error">Erro: ${result.error}</pre>`
      }
    } catch (error) {
      outputDiv.innerHTML = `<pre class="output-error">Erro de conexão: ${error}</pre>`
    }
  }
})
</script>

<style scoped>
.blocknote-editor {
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

/* Toolbar */
.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #e9e9e7;
  background: #f7f6f3;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.toolbar-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.toolbar-btn.active {
  background: #2383e2;
  color: white;
}

.toolbar-icon {
  width: 16px;
  height: 16px;
}

/* Editor */
.editor-container {
  min-height: 400px;
  position: relative;
}

.editor-content {
  padding: 24px;
  min-height: 376px;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #37352f;
}

.editor-content:empty::before {
  content: attr(placeholder);
  color: #c9c7c4;
  pointer-events: none;
}

/* Block Styles */
.editor-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1rem 0;
  color: #37352f;
}

.editor-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0.8rem 0;
  color: #37352f;
}

.editor-content p {
  margin: 0.5rem 0;
}

.editor-content ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.editor-content li {
  margin: 0.25rem 0;
}

/* Code Blocks */
:deep(.code-block) {
  margin: 16px 0;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  overflow: hidden;
  background: #f7f6f3;
}

:deep(.code-header) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #e9e9e7;
  font-size: 12px;
  color: #787774;
}

:deep(.code-language) {
  font-weight: 500;
  text-transform: uppercase;
}

:deep(.execute-btn) {
  background: #2383e2;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
}

:deep(.execute-btn:hover) {
  background: #1a6cc7;
}

:deep(.code-content) {
  padding: 12px;
  margin: 0;
  background: #f7f6f3;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 14px;
  line-height: 1.4;
  overflow-x: auto;
}

:deep(.code-output) {
  border-top: 1px solid #e9e9e7;
  padding: 12px;
  background: #fafafa;
}

:deep(.output-success) {
  color: #0f7b0f;
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 13px;
  white-space: pre-wrap;
}

:deep(.output-error) {
  color: #e03e3e;
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 13px;
  white-space: pre-wrap;
}

:deep(.loading) {
  color: #787774;
  font-size: 13px;
  font-style: italic;
}

/* Math Blocks */
:deep(.math-block) {
  margin: 16px 0;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-family: 'Times New Roman', serif;
  text-align: center;
  color: #0c4a6e;
}

/* Code Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.code-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.code-modal h3 {
  margin: 0 0 16px 0;
  color: #37352f;
}

.language-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  margin-bottom: 12px;
  font-size: 14px;
}

.code-textarea {
  width: 100%;
  height: 200px;
  padding: 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 16px;
}

.modal-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid;
}

.btn-primary {
  background: #2383e2;
  color: white;
  border-color: #2383e2;
}

.btn-primary:hover {
  background: #1a6cc7;
}

.btn-secondary {
  background: white;
  color: #37352f;
  border-color: #e9e9e7;
}

.btn-secondary:hover {
  background: #f7f6f3;
}
</style>
