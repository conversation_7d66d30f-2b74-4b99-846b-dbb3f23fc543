<template>
  <div 
    class="code-block"
    :class="{ 'code-block--focused': isFocused, 'code-block--executing': isExecuting }"
  >
    <!-- Code Header -->
    <div class="code-header">
      <div class="header-left">
        <div class="language-selector">
          <select
            v-model="selectedLanguage"
            @change="updateLanguage"
            class="language-select"
          >
            <option value="python">Python</option>
            <option value="javascript">JavaScript</option>
            <option value="typescript">TypeScript</option>
            <option value="bash">Bash</option>
          </select>
        </div>
        
        <div class="execution-status">
          <div
            v-if="isExecuting"
            class="status-indicator status-indicator--running"
          >
            <div class="running-dot"></div>
            Executando...
          </div>
          <div
            v-else-if="executionResult"
            class="status-indicator"
            :class="{
              'status-indicator--success': executionResult.success,
              'status-indicator--error': !executionResult.success
            }"
          >
            <CheckCircleIcon v-if="executionResult.success" class="status-icon" />
            <AlertCircleIcon v-else class="status-icon" />
            {{ executionResult.success ? 'Executado' : 'Erro' }}
          </div>
        </div>
      </div>
      
      <div class="header-right">
        <!-- AI Assistant Button -->
        <button
          @click="toggleAIAssistant"
          class="header-btn"
          :class="{ 'header-btn--active': showAIAssistant }"
          title="Assistente IA"
        >
          <BotIcon class="btn-icon" />
        </button>
        
        <!-- Copy Button -->
        <button
          @click="copyCode"
          class="header-btn"
          title="Copiar código"
        >
          <CopyIcon class="btn-icon" />
        </button>
        
        <!-- Fullscreen Button -->
        <button
          @click="toggleFullscreen"
          class="header-btn"
          title="Tela cheia"
        >
          <MaximizeIcon class="btn-icon" />
        </button>
        
        <!-- Execute Button -->
        <button
          v-if="selectedLanguage === 'python'"
          @click="executeCode"
          class="execute-btn"
          :disabled="isExecuting || !codeContent.trim()"
          title="Executar código"
        >
          <PlayIcon class="btn-icon" />
          {{ isExecuting ? 'Executando...' : 'Executar' }}
        </button>
      </div>
    </div>
    
    <!-- AI Assistant Panel -->
    <div 
      v-if="showAIAssistant"
      class="ai-assistant"
    >
      <div class="assistant-header">
        <BotIcon class="assistant-icon" />
        <h4>Assistente de Código</h4>
        <button
          @click="showAIAssistant = false"
          class="assistant-close"
        >
          <XIcon class="close-icon" />
        </button>
      </div>
      
      <div class="assistant-actions">
        <button
          @click="aiAction('explain')"
          class="ai-btn"
          :disabled="isAIWorking || !codeContent.trim()"
        >
          <MessageCircleIcon class="ai-icon" />
          Explicar
        </button>
        
        <button
          @click="aiAction('optimize')"
          class="ai-btn"
          :disabled="isAIWorking || !codeContent.trim()"
        >
          <ZapIcon class="ai-icon" />
          Otimizar
        </button>
        
        <button
          @click="aiAction('fix')"
          class="ai-btn"
          :disabled="isAIWorking || !codeContent.trim()"
        >
          <WrenchIcon class="ai-icon" />
          Corrigir
        </button>
        
        <button
          @click="aiAction('comment')"
          class="ai-btn"
          :disabled="isAIWorking || !codeContent.trim()"
        >
          <FileTextIcon class="ai-icon" />
          Comentar
        </button>
      </div>
      
      <div 
        v-if="aiResponse"
        class="ai-response"
      >
        <div class="response-header">
          <span class="response-title">{{ aiResponse.action }}</span>
        </div>
        <div class="response-content">
          <pre v-if="aiResponse.type === 'code'" class="response-code">{{ aiResponse.content }}</pre>
          <p v-else class="response-text">{{ aiResponse.content }}</p>
        </div>
        <div 
          v-if="aiResponse.type === 'code'"
          class="response-actions"
        >
          <button
            @click="applyAISuggestion"
            class="response-btn response-btn--primary"
          >
            Aplicar
          </button>
          <button
            @click="aiResponse = null"
            class="response-btn response-btn--secondary"
          >
            Descartar
          </button>
        </div>
      </div>
      
      <div 
        v-if="isAIWorking"
        class="ai-loading"
      >
        <div class="loading-spinner"></div>
        <span>Processando...</span>
      </div>
    </div>
    
    <!-- Code Editor -->
    <div 
      class="code-editor"
      :class="{ 'code-editor--fullscreen': isFullscreen }"
    >
      <div class="editor-container">
        <textarea
          ref="codeTextarea"
          v-model="codeContent"
          @input="handleCodeChange"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="handleKeyDown"
          class="code-textarea"
          :placeholder="getPlaceholder()"
          spellcheck="false"
          autocomplete="off"
        ></textarea>
        
        <!-- Line Numbers -->
        <div class="line-numbers">
          <div
            v-for="line in lineCount"
            :key="line"
            class="line-number"
          >
            {{ line }}
          </div>
        </div>
        
        <!-- Syntax Highlighting Overlay -->
        <div class="syntax-overlay">
          <pre 
            class="syntax-content"
            v-html="highlightedCode"
          ></pre>
        </div>
      </div>
      
      <!-- Fullscreen Exit Button -->
      <button
        v-if="isFullscreen"
        @click="exitFullscreen"
        class="fullscreen-exit"
        title="Sair da tela cheia"
      >
        <MinimizeIcon class="btn-icon" />
      </button>
    </div>
    
    <!-- Execution Output -->
    <div 
      v-if="executionResult"
      class="code-output"
      :class="{
        'code-output--success': executionResult.success,
        'code-output--error': !executionResult.success
      }"
    >
      <div class="output-header">
        <span class="output-title">
          {{ executionResult.success ? 'Resultado' : 'Erro' }}
        </span>
        <span class="output-time">
          Executado em {{ formatExecutionTime(executionResult.executionTime) }}
        </span>
        <button
          @click="clearOutput"
          class="output-clear"
          title="Limpar resultado"
        >
          <XIcon class="btn-icon" />
        </button>
      </div>
      
      <pre class="output-content">{{ executionResult.output }}</pre>
      
      <!-- Output Actions -->
      <div class="output-actions">
        <button
          @click="copyOutput"
          class="output-btn"
          title="Copiar resultado"
        >
          <CopyIcon class="btn-icon" />
          Copiar
        </button>
      </div>
    </div>
    
    <!-- Block Actions -->
    <div 
      v-if="isFocused && !isFullscreen"
      class="block-actions"
    >
      <button
        @click="duplicateBlock"
        class="action-btn"
        title="Duplicar bloco"
      >
        <CopyIcon class="action-icon" />
      </button>
      
      <button
        @click="deleteBlock"
        class="action-btn action-btn--danger"
        title="Excluir bloco"
      >
        <TrashIcon class="action-icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import {
  Play as PlayIcon,
  Copy as CopyIcon,
  Maximize as MaximizeIcon,
  Minimize as MinimizeIcon,
  Bot as BotIcon,
  X as XIcon,
  CheckCircle as CheckCircleIcon,
  AlertCircle as AlertCircleIcon,
  MessageCircle as MessageCircleIcon,
  Zap as ZapIcon,
  Wrench as WrenchIcon,
  FileText as FileTextIcon,
  Trash2 as TrashIcon
} from 'lucide-vue-next'

export interface CodeBlockData {
  id: string
  type: 'code'
  content: {
    code: string
    language: string
    output?: string
    success?: boolean
    executionTime?: number
  }
}

interface Props {
  block: CodeBlockData
  index?: number
}

interface Emits {
  update: [block: CodeBlockData]
  delete: [id: string]
  duplicate: [block: CodeBlockData]
  focus: [id: string]
}

interface ExecutionResult {
  success: boolean
  output: string
  executionTime: number
}

interface AIResponse {
  action: string
  type: 'code' | 'text'
  content: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const codeTextarea = ref<HTMLTextAreaElement | null>(null)
const isFocused = ref(false)
const isExecuting = ref(false)
const isFullscreen = ref(false)
const showAIAssistant = ref(false)
const isAIWorking = ref(false)
const executionResult = ref<ExecutionResult | null>(null)
const aiResponse = ref<AIResponse | null>(null)
const codeContent = ref('')
const selectedLanguage = ref('python')

// Computed
const lineCount = computed(() => {
  return codeContent.value.split('\n').length
})

const highlightedCode = computed(() => {
  return highlightSyntax(codeContent.value, selectedLanguage.value)
})

// Methods
function handleFocus() {
  isFocused.value = true
  emit('focus', props.block.id)
}

function handleBlur() {
  isFocused.value = false
}

function handleCodeChange() {
  updateBlock({
    ...props.block,
    content: {
      ...props.block.content,
      code: codeContent.value
    }
  })
}

function updateLanguage() {
  updateBlock({
    ...props.block,
    content: {
      ...props.block.content,
      language: selectedLanguage.value
    }
  })
}

function handleKeyDown(event: KeyboardEvent) {
  // Tab handling
  if (event.key === 'Tab') {
    event.preventDefault()
    const textarea = event.target as HTMLTextAreaElement
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    
    const spaces = '  ' // 2 spaces for indentation
    codeContent.value = codeContent.value.substring(0, start) + spaces + codeContent.value.substring(end)
    
    nextTick(() => {
      textarea.selectionStart = textarea.selectionEnd = start + spaces.length
    })
  }
  
  // Auto-indentation on Enter
  if (event.key === 'Enter') {
    event.preventDefault()
    const textarea = event.target as HTMLTextAreaElement
    const start = textarea.selectionStart
    const lines = codeContent.value.substring(0, start).split('\n')
    const currentLine = lines[lines.length - 1]
    const indentMatch = currentLine.match(/^(\s*)/)
    const currentIndent = indentMatch ? indentMatch[1] : ''
    
    // Add extra indent for certain endings
    let extraIndent = ''
    if (currentLine.trim().endsWith(':')) {
      extraIndent = '  '
    }
    
    const newText = '\n' + currentIndent + extraIndent
    codeContent.value = codeContent.value.substring(0, start) + newText + codeContent.value.substring(start)
    
    nextTick(() => {
      textarea.selectionStart = textarea.selectionEnd = start + newText.length
    })
  }
}

async function executeCode() {
  if (!codeContent.value.trim() || selectedLanguage.value !== 'python') return
  
  isExecuting.value = true
  executionResult.value = null
  
  const startTime = Date.now()
  
  try {
    const response = await fetch('http://localhost:5002/api/code/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: codeContent.value,
        language: selectedLanguage.value
      })
    })
    
    const result = await response.json()
    const executionTime = Date.now() - startTime
    
    executionResult.value = {
      success: result.success || false,
      output: result.output || result.error || 'Sem resultado',
      executionTime
    }
    
    // Update block with execution result
    updateBlock({
      ...props.block,
      content: {
        ...props.block.content,
        output: executionResult.value.output,
        success: executionResult.value.success,
        executionTime: executionResult.value.executionTime
      }
    })
    
  } catch (error) {
    const executionTime = Date.now() - startTime
    executionResult.value = {
      success: false,
      output: `Erro de conexão: ${error}`,
      executionTime
    }
  } finally {
    isExecuting.value = false
  }
}

async function aiAction(action: string) {
  if (!codeContent.value.trim() || isAIWorking.value) return
  
  isAIWorking.value = true
  aiResponse.value = null
  
  try {
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock AI responses based on action
    let response: AIResponse
    
    switch (action) {
      case 'explain':
        response = {
          action: 'Explicação do Código',
          type: 'text',
          content: 'Este código define uma função que calcula números na sequência de Fibonacci usando recursão. É uma implementação simples mas não otimizada para números grandes.'
        }
        break
      
      case 'optimize':
        response = {
          action: 'Versão Otimizada',
          type: 'code',
          content: `def fibonacci_optimized(n, memo={}):\n    if n in memo:\n        return memo[n]\n    if n <= 1:\n        return n\n    memo[n] = fibonacci_optimized(n-1, memo) + fibonacci_optimized(n-2, memo)\n    return memo[n]\n\n# Exemplo de uso\nfor i in range(10):\n    print(f"F({i}) = {fibonacci_optimized(i)}")`
        }
        break
      
      case 'fix':
        response = {
          action: 'Código Corrigido',
          type: 'code',
          content: codeContent.value.replace(/def /g, 'def ').replace(/print/g, 'print')
        }
        break
      
      case 'comment':
        response = {
          action: 'Código Comentado',
          type: 'code',
          content: `# Função para calcular números de Fibonacci\n${codeContent.value.split('\n').map(line => line.trim() ? `${line}  # Explicação da linha` : line).join('\n')}`
        }
        break
      
      default:
        response = {
          action: 'Resposta IA',
          type: 'text',
          content: 'Ação não reconhecida.'
        }
    }
    
    aiResponse.value = response
    
  } catch (error) {
    console.error('AI Error:', error)
  } finally {
    isAIWorking.value = false
  }
}

function applyAISuggestion() {
  if (aiResponse.value && aiResponse.value.type === 'code') {
    codeContent.value = aiResponse.value.content
    handleCodeChange()
    aiResponse.value = null
  }
}

function toggleAIAssistant() {
  showAIAssistant.value = !showAIAssistant.value
  if (!showAIAssistant.value) {
    aiResponse.value = null
  }
}

function copyCode() {
  navigator.clipboard.writeText(codeContent.value).then(() => {
    // Could show a toast notification here
  })
}

function copyOutput() {
  if (executionResult.value) {
    navigator.clipboard.writeText(executionResult.value.output)
  }
}

function clearOutput() {
  executionResult.value = null
}

function toggleFullscreen() {
  isFullscreen.value = true
  document.body.classList.add('fullscreen-mode')
  nextTick(() => {
    codeTextarea.value?.focus()
  })
}

function exitFullscreen() {
  isFullscreen.value = false
  document.body.classList.remove('fullscreen-mode')
}

function formatExecutionTime(time: number): string {
  if (time < 1000) {
    return `${time}ms`
  }
  return `${(time / 1000).toFixed(2)}s`
}

function getPlaceholder(): string {
  const placeholders = {
    python: '# Digite seu código Python aqui...\nprint("Hello, World!")',
    javascript: '// Digite seu código JavaScript aqui...\nconsole.log("Hello, World!");',
    typescript: '// Digite seu código TypeScript aqui...\nconst message: string = "Hello, World!";\nconsole.log(message);',
    bash: '# Digite seus comandos bash aqui...\necho "Hello, World!"'
  }
  return placeholders[selectedLanguage.value as keyof typeof placeholders] || 'Digite seu código aqui...'
}

function highlightSyntax(code: string, language: string): string {
  // Simple syntax highlighting - in production, use a proper library like Prism.js
  if (!code) return ''
  
  let highlighted = code
  
  if (language === 'python') {
    // Python keywords
    highlighted = highlighted.replace(/(def|class|if|else|elif|for|while|try|except|import|from|return|yield|with|as)\b/g, '<span class="keyword">$1</span>')
    // Strings
    highlighted = highlighted.replace(/(".*?"|'.*?')/g, '<span class="string">$1</span>')
    // Comments
    highlighted = highlighted.replace(/(#.*$)/gm, '<span class="comment">$1</span>')
    // Numbers
    highlighted = highlighted.replace(/\b(\d+)\b/g, '<span class="number">$1</span>')
  }
  
  return highlighted
}

function duplicateBlock() {
  emit('duplicate', props.block)
}

function deleteBlock() {
  emit('delete', props.block.id)
}

function updateBlock(updatedBlock: CodeBlockData) {
  emit('update', updatedBlock)
}

// Handle escape key
function handleGlobalKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && isFullscreen.value) {
    exitFullscreen()
  }
}

// Handle clicks outside
function handleClickOutside(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.code-block')) {
    isFocused.value = false
    if (!isFullscreen.value) {
      showAIAssistant.value = false
    }
  }
}

// Watchers
watch(() => props.block.content, (newContent) => {
  codeContent.value = newContent.code || ''
  selectedLanguage.value = newContent.language || 'python'
  
  if (newContent.output && newContent.success !== undefined) {
    executionResult.value = {
      success: newContent.success,
      output: newContent.output,
      executionTime: newContent.executionTime || 0
    }
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleGlobalKeydown)
  
  // Initialize from block content
  codeContent.value = props.block.content.code || ''
  selectedLanguage.value = props.block.content.language || 'python'
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleGlobalKeydown)
  document.body.classList.remove('fullscreen-mode')
})
</script>

<style scoped>
.code-block {
  position: relative;
  margin: 8px 0;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  background: white;
  overflow: hidden;
  transition: all 0.15s ease;
}

.code-block--focused {
  border-color: #2383e2;
  box-shadow: 0 0 0 2px rgba(35, 131, 226, 0.1);
}

.code-block--executing {
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
}

/* Code Header */
.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f7f6f3;
  border-bottom: 1px solid #e9e9e7;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.language-selector {
  position: relative;
}

.language-select {
  padding: 4px 8px;
  border: 1px solid #d3d1cb;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: white;
  color: #37352f;
  cursor: pointer;
}

.execution-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.status-indicator--running {
  color: #f59e0b;
}

.status-indicator--success {
  color: #10b981;
}

.status-indicator--error {
  color: #ef4444;
}

.running-dot {
  width: 6px;
  height: 6px;
  background: #f59e0b;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-icon {
  width: 12px;
  height: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.header-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.header-btn--active {
  background: #2383e2;
}

.header-btn--active .btn-icon {
  color: white;
}

.execute-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.execute-btn:hover:not(:disabled) {
  background: #059669;
}

.execute-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-icon {
  width: 14px;
  height: 14px;
  color: #787774;
}

/* AI Assistant */
.ai-assistant {
  border-bottom: 1px solid #e9e9e7;
  background: #fafafa;
}

.assistant-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #e9e9e7;
}

.assistant-icon {
  width: 16px;
  height: 16px;
  color: #2383e2;
}

.assistant-header h4 {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #37352f;
  margin: 0;
}

.assistant-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  border-radius: 2px;
  cursor: pointer;
}

.close-icon {
  width: 12px;
  height: 12px;
  color: #9b9a97;
}

.assistant-actions {
  display: flex;
  gap: 8px;
  padding: 12px;
}

.ai-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 4px;
  background: white;
  color: #37352f;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.ai-btn:hover:not(:disabled) {
  border-color: #2383e2;
  background: rgba(35, 131, 226, 0.04);
}

.ai-btn:disabled {
  background: #f7f6f3;
  color: #9b9a97;
  cursor: not-allowed;
}

.ai-icon {
  width: 12px;
  height: 12px;
}

.ai-response {
  margin: 12px;
  padding: 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  background: white;
}

.response-header {
  margin-bottom: 8px;
}

.response-title {
  font-size: 12px;
  font-weight: 600;
  color: #2383e2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.response-content {
  margin-bottom: 12px;
}

.response-code {
  background: #f7f6f3;
  border: 1px solid #e9e9e7;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  font-family: 'SF Mono', Monaco, monospace;
  margin: 0;
  white-space: pre-wrap;
}

.response-text {
  font-size: 13px;
  line-height: 1.4;
  color: #37352f;
  margin: 0;
}

.response-actions {
  display: flex;
  gap: 8px;
}

.response-btn {
  padding: 6px 12px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.response-btn--primary {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.response-btn--primary:hover {
  background: #059669;
}

.response-btn--secondary {
  background: white;
  border-color: #e9e9e7;
  color: #37352f;
}

.response-btn--secondary:hover {
  background: #f7f6f3;
}

.ai-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #787774;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e9e9e7;
  border-top: 2px solid #2383e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Code Editor */
.code-editor {
  position: relative;
  min-height: 120px;
}

.code-editor--fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  border-radius: 0;
  min-height: 100vh;
}

.editor-container {
  position: relative;
  display: flex;
}

.line-numbers {
  background: #f7f6f3;
  border-right: 1px solid #e9e9e7;
  padding: 16px 8px;
  min-width: 40px;
  text-align: right;
  user-select: none;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #9b9a97;
}

.line-number {
  height: 19.5px;
}

.code-textarea {
  flex: 1;
  padding: 16px;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 13px;
  line-height: 1.5;
  color: transparent;
  caret-color: #2383e2;
  min-height: 120px;
}

.syntax-overlay {
  position: absolute;
  top: 0;
  left: 40px;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.syntax-content {
  padding: 16px;
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #37352f;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Syntax highlighting */
:deep(.keyword) {
  color: #8b5cf6;
  font-weight: 600;
}

:deep(.string) {
  color: #10b981;
}

:deep(.comment) {
  color: #6b7280;
  font-style: italic;
}

:deep(.number) {
  color: #f59e0b;
}

.fullscreen-exit {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Code Output */
.code-output {
  border-top: 1px solid #e9e9e7;
  background: #fafafa;
}

.code-output--success {
  background: rgba(16, 185, 129, 0.04);
  border-top-color: rgba(16, 185, 129, 0.2);
}

.code-output--error {
  background: rgba(239, 68, 68, 0.04);
  border-top-color: rgba(239, 68, 68, 0.2);
}

.output-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid #e9e9e7;
  background: rgba(255, 255, 255, 0.5);
}

.output-title {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-output--success .output-title {
  color: #059669;
}

.code-output--error .output-title {
  color: #dc2626;
}

.output-time {
  font-size: 11px;
  color: #9b9a97;
}

.output-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  border-radius: 2px;
  cursor: pointer;
}

.output-content {
  padding: 12px;
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.code-output--success .output-content {
  color: #047857;
}

.code-output--error .output-content {
  color: #dc2626;
}

.output-actions {
  padding: 8px 12px;
  display: flex;
  gap: 8px;
}

.output-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #e9e9e7;
  border-radius: 4px;
  background: white;
  color: #37352f;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.output-btn:hover {
  border-color: #d3d1cb;
  background: #f7f6f3;
}

/* Block Actions */
.block-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  background: white;
  padding: 4px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9e9e7;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.action-btn--danger:hover {
  background: rgba(224, 62, 62, 0.1);
}

.action-icon {
  width: 14px;
  height: 14px;
  color: #787774;
}

.action-btn--danger .action-icon {
  color: #e03e3e;
}

/* Global fullscreen styles */
:global(.fullscreen-mode) {
  overflow: hidden;
}

/* Responsive */
@media (max-width: 768px) {
  .code-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .header-left,
  .header-right {
    justify-content: center;
  }
  
  .assistant-actions {
    flex-wrap: wrap;
  }
  
  .ai-btn {
    flex: 1;
    min-width: 0;
  }
  
  .line-numbers {
    min-width: 30px;
    font-size: 11px;
  }
  
  .code-textarea,
  .syntax-content {
    font-size: 12px;
  }
}
</style>
