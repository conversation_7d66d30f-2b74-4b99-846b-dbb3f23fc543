<template>
  <div 
    class="image-block"
    :class="{ 'image-block--focused': isFocused }"
  >
    <!-- Image Upload/URL Input -->
    <div 
      v-if="!block.content.url"
      class="image-upload"
    >
      <div class="upload-area">
        <div class="upload-content">
          <ImageIcon class="upload-icon" />
          <h3 class="upload-title">Adicionar imagem</h3>
          <p class="upload-description">
            Faça upload de uma imagem ou cole uma URL
          </p>
          
          <div class="upload-actions">
            <button
              @click="triggerFileUpload"
              class="upload-btn upload-btn--primary"
            >
              <UploadIcon class="btn-icon" />
              Upload de arquivo
            </button>
            
            <button
              @click="showUrlInput = true"
              class="upload-btn upload-btn--secondary"
            >
              <LinkIcon class="btn-icon" />
              URL da imagem
            </button>
          </div>
          
          <!-- URL Input -->
          <div 
            v-if="showUrlInput"
            class="url-input-container"
          >
            <input
              ref="urlInput"
              v-model="imageUrl"
              @keydown.enter="loadImageFromUrl"
              @keydown.escape="cancelUrlInput"
              type="url"
              placeholder="Cole a URL da imagem aqui..."
              class="url-input"
            />
            <div class="url-actions">
              <button 
                @click="loadImageFromUrl"
                class="url-btn url-btn--primary"
                :disabled="!imageUrl.trim()"
              >
                Carregar
              </button>
              <button 
                @click="cancelUrlInput"
                class="url-btn url-btn--secondary"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="handleFileUpload"
        style="display: none"
      />
    </div>
    
    <!-- Image Display -->
    <div 
      v-else
      class="image-display"
      @click="focusBlock"
    >
      <div class="image-container">
        <img
          :src="block.content.url"
          :alt="block.content.caption || 'Imagem'"
          class="image"
          :class="{ 'image--loading': isLoading }"
          @load="handleImageLoad"
          @error="handleImageError"
        />
        
        <!-- Loading Overlay -->
        <div 
          v-if="isLoading"
          class="image-loading"
        >
          <div class="loading-spinner"></div>
        </div>
        
        <!-- Error Overlay -->
        <div 
          v-if="hasError"
          class="image-error"
        >
          <AlertCircleIcon class="error-icon" />
          <p class="error-text">Erro ao carregar imagem</p>
          <button
            @click="retryImageLoad"
            class="retry-btn"
          >
            Tentar novamente
          </button>
        </div>
        
        <!-- Resize Handles -->
        <div 
          v-if="isFocused && !isLoading && !hasError"
          class="resize-handles"
        >
          <div 
            class="resize-handle resize-handle--nw"
            @mousedown="(e) => startResize(e, 'nw')"
          ></div>
          <div 
            class="resize-handle resize-handle--ne"
            @mousedown="(e) => startResize(e, 'ne')"
          ></div>
          <div 
            class="resize-handle resize-handle--sw"
            @mousedown="(e) => startResize(e, 'sw')"
          ></div>
          <div 
            class="resize-handle resize-handle--se"
            @mousedown="(e) => startResize(e, 'se')"
          ></div>
        </div>
      </div>
      
      <!-- Caption Input -->
      <div class="caption-container">
        <input
          v-model="imageCaption"
          @blur="updateCaption"
          @keydown.enter="updateCaption"
          type="text"
          placeholder="Digite uma legenda..."
          class="caption-input"
        />
      </div>
    </div>
    
    <!-- Block Actions -->
    <div 
      v-if="isFocused"
      class="block-actions"
    >
      <button
        v-if="block.content.url"
        @click="replaceImage"
        class="action-btn"
        title="Substituir imagem"
      >
        <ImageIcon class="action-icon" />
      </button>
      
      <button
        @click="duplicateBlock"
        class="action-btn"
        title="Duplicar bloco"
      >
        <CopyIcon class="action-icon" />
      </button>
      
      <button
        @click="deleteBlock"
        class="action-btn action-btn--danger"
        title="Excluir bloco"
      >
        <TrashIcon class="action-icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import {
  Image as ImageIcon,
  Upload as UploadIcon,
  Link as LinkIcon,
  Copy as CopyIcon,
  Trash2 as TrashIcon,
  AlertCircle as AlertCircleIcon
} from 'lucide-vue-next'

export interface ImageBlockData {
  id: string
  type: 'image'
  content: {
    url: string
    caption?: string
    width?: number
    height?: number
    originalWidth?: number
    originalHeight?: number
  }
}

interface Props {
  block: ImageBlockData
  index?: number
}

interface Emits {
  update: [block: ImageBlockData]
  delete: [id: string]
  duplicate: [block: ImageBlockData]
  focus: [id: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const fileInput = ref<HTMLInputElement | null>(null)
const urlInput = ref<HTMLInputElement | null>(null)
const isFocused = ref(false)
const showUrlInput = ref(false)
const imageUrl = ref('')
const imageCaption = ref('')
const isLoading = ref(false)
const hasError = ref(false)
const isResizing = ref(false)

// Computed
const currentImageUrl = computed(() => props.block.content.url)

// Methods
function focusBlock() {
  isFocused.value = true
  emit('focus', props.block.id)
}

function triggerFileUpload() {
  fileInput.value?.click()
}

async function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('Por favor, selecione uma imagem válida.')
    return
  }
  
  // Validate file size (max 10MB)
  if (file.size > 10 * 1024 * 1024) {
    alert('A imagem é muito grande. O tamanho máximo é 10MB.')
    return
  }
  
  isLoading.value = true
  hasError.value = false
  
  try {
    // Convert file to data URL for now (in production, upload to server)
    const reader = new FileReader()
    reader.onload = (e) => {
      const dataUrl = e.target?.result as string
      
      // Create image to get dimensions
      const img = new Image()
      img.onload = () => {
        updateBlock({
          ...props.block,
          content: {
            ...props.block.content,
            url: dataUrl,
            originalWidth: img.naturalWidth,
            originalHeight: img.naturalHeight,
            width: Math.min(img.naturalWidth, 800),
            height: Math.min(img.naturalHeight, 600)
          }
        })
        isLoading.value = false
      }
      img.onerror = () => {
        hasError.value = true
        isLoading.value = false
      }
      img.src = dataUrl
    }
    reader.readAsDataURL(file)
  } catch (error) {
    console.error('Error uploading file:', error)
    hasError.value = true
    isLoading.value = false
  }
  
  // Reset file input
  target.value = ''
}

async function loadImageFromUrl() {
  if (!imageUrl.value.trim()) return
  
  isLoading.value = true
  hasError.value = false
  
  try {
    // Validate URL format
    const url = new URL(imageUrl.value)
    if (!['http:', 'https:'].includes(url.protocol)) {
      throw new Error('URL deve começar com http:// ou https://')
    }
    
    // Create image to validate and get dimensions
    const img = new Image()
    img.onload = () => {
      updateBlock({
        ...props.block,
        content: {
          ...props.block.content,
          url: imageUrl.value,
          originalWidth: img.naturalWidth,
          originalHeight: img.naturalHeight,
          width: Math.min(img.naturalWidth, 800),
          height: Math.min(img.naturalHeight, 600)
        }
      })
      isLoading.value = false
      showUrlInput.value = false
      imageUrl.value = ''
    }
    img.onerror = () => {
      hasError.value = true
      isLoading.value = false
        alert('Não foi possível carregar a imagem desta URL.')
    }
    img.crossOrigin = 'anonymous'
    img.src = imageUrl.value
  } catch (error) {
    hasError.value = true
    isLoading.value = false
    alert('URL inválida. Por favor, verifique o endereço.')
  }
}

function cancelUrlInput() {
  showUrlInput.value = false
  imageUrl.value = ''
}

function handleImageLoad() {
  isLoading.value = false
  hasError.value = false
}

function handleImageError() {
  isLoading.value = false
  hasError.value = true
}

function retryImageLoad() {
  if (currentImageUrl.value) {
    isLoading.value = true
    hasError.value = false
    
    // Force reload image
    const img = new Image()
    img.onload = handleImageLoad
    img.onerror = handleImageError
    img.src = currentImageUrl.value + '?retry=' + Date.now()
  }
}

function updateCaption() {
  updateBlock({
    ...props.block,
    content: {
      ...props.block.content,
      caption: imageCaption.value
    }
  })
}

function replaceImage() {
  // Reset block content to show upload interface
  updateBlock({
    ...props.block,
    content: {
      url: '',
      caption: props.block.content.caption
    }
  })
}

function duplicateBlock() {
  emit('duplicate', props.block)
}

function deleteBlock() {
  emit('delete', props.block.id)
}

function updateBlock(updatedBlock: ImageBlockData) {
  emit('update', updatedBlock)
}

// Resize functionality
let resizeStartData: {
  startX: number
  startY: number
  startWidth: number
  startHeight: number
  direction: string
} | null = null

function startResize(event: MouseEvent, direction: string) {
  event.preventDefault()
  event.stopPropagation()
  
  isResizing.value = true
  resizeStartData = {
    startX: event.clientX,
    startY: event.clientY,
    startWidth: props.block.content.width || 400,
    startHeight: props.block.content.height || 300,
    direction
  }
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = getResizeCursor(direction)
}

function handleResize(event: MouseEvent) {
  if (!resizeStartData) return
  
  const deltaX = event.clientX - resizeStartData.startX
  const deltaY = event.clientY - resizeStartData.startY
  
  let newWidth = resizeStartData.startWidth
  let newHeight = resizeStartData.startHeight
  
  const aspectRatio = (props.block.content.originalWidth || 1) / (props.block.content.originalHeight || 1)
  
  // Calculate new dimensions based on resize direction
  switch (resizeStartData.direction) {
    case 'se': // Southeast
      newWidth = Math.max(200, resizeStartData.startWidth + deltaX)
      newHeight = newWidth / aspectRatio
      break
    case 'sw': // Southwest
      newWidth = Math.max(200, resizeStartData.startWidth - deltaX)
      newHeight = newWidth / aspectRatio
      break
    case 'ne': // Northeast
      newWidth = Math.max(200, resizeStartData.startWidth + deltaX)
      newHeight = newWidth / aspectRatio
      break
    case 'nw': // Northwest
      newWidth = Math.max(200, resizeStartData.startWidth - deltaX)
      newHeight = newWidth / aspectRatio
      break
  }
  
  // Limit maximum size
  const maxWidth = 1200
  if (newWidth > maxWidth) {
    newWidth = maxWidth
    newHeight = newWidth / aspectRatio
  }
  
  updateBlock({
    ...props.block,
    content: {
      ...props.block.content,
      width: Math.round(newWidth),
      height: Math.round(newHeight)
    }
  })
}

function stopResize() {
  isResizing.value = false
  resizeStartData = null
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
}

function getResizeCursor(direction: string): string {
  const cursors = {
    'nw': 'nw-resize',
    'ne': 'ne-resize',
    'sw': 'sw-resize',
    'se': 'se-resize'
  }
  return cursors[direction as keyof typeof cursors] || 'default'
}

// Handle clicks outside
function handleClickOutside(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.image-block')) {
    isFocused.value = false
  }
}

// Lifecycle
onMounted(() => {
  imageCaption.value = props.block.content.caption || ''
  document.addEventListener('click', handleClickOutside)
  
  if (showUrlInput.value) {
    nextTick(() => {
      urlInput.value?.focus()
    })
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped>
.image-block {
  position: relative;
  margin: 8px 0;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.15s ease;
}

.image-block--focused {
  background: rgba(35, 131, 226, 0.04);
}

/* Upload Area */
.image-upload {
  border: 2px dashed #e9e9e7;
  border-radius: 12px;
  padding: 40px 24px;
  text-align: center;
  transition: all 0.15s ease;
}

.image-upload:hover {
  border-color: #d3d1cb;
  background: rgba(247, 246, 243, 0.5);
}

.upload-content {
  max-width: 400px;
  margin: 0 auto;
}

.upload-icon {
  width: 48px;
  height: 48px;
  color: #c9c7c4;
  margin-bottom: 16px;
}

.upload-title {
  font-size: 18px;
  font-weight: 600;
  color: #37352f;
  margin: 0 0 8px 0;
}

.upload-description {
  font-size: 14px;
  color: #787774;
  margin: 0 0 24px 0;
}

.upload-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 16px;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.upload-btn--primary {
  background: #2383e2;
  border-color: #2383e2;
  color: white;
}

.upload-btn--primary:hover {
  background: #1a6cc7;
  border-color: #1a6cc7;
}

.upload-btn--secondary {
  background: white;
  border-color: #e9e9e7;
  color: #37352f;
}

.upload-btn--secondary:hover {
  background: #f7f6f3;
  border-color: #d3d1cb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* URL Input */
.url-input-container {
  margin-top: 16px;
}

.url-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 12px;
  outline: none;
  transition: border-color 0.15s ease;
}

.url-input:focus {
  border-color: #2383e2;
  box-shadow: 0 0 0 2px rgba(35, 131, 226, 0.1);
}

.url-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.url-btn {
  padding: 8px 16px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.url-btn--primary {
  background: #2383e2;
  border-color: #2383e2;
  color: white;
}

.url-btn--primary:hover:not(:disabled) {
  background: #1a6cc7;
}

.url-btn--primary:disabled {
  background: #c9c7c4;
  border-color: #c9c7c4;
  cursor: not-allowed;
}

.url-btn--secondary {
  background: white;
  border-color: #e9e9e7;
  color: #37352f;
}

.url-btn--secondary:hover {
  background: #f7f6f3;
}

/* Image Display */
.image-display {
  text-align: center;
}

.image-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
}

.image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: opacity 0.15s ease;
}

.image--loading {
  opacity: 0.7;
}

/* Loading Overlay */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9e9e7;
  border-top: 3px solid #2383e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Error Overlay */
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(224, 62, 62, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px solid rgba(224, 62, 62, 0.2);
}

.error-icon {
  width: 32px;
  height: 32px;
  color: #e03e3e;
  margin-bottom: 8px;
}

.error-text {
  color: #e03e3e;
  font-size: 14px;
  margin: 0 0 12px 0;
}

.retry-btn {
  background: #e03e3e;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #c73636;
}

/* Resize Handles */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #2383e2;
  border: 2px solid white;
  border-radius: 50%;
  pointer-events: auto;
  cursor: pointer;
}

.resize-handle--nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-handle--ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-handle--sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-handle--se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* Caption */
.caption-container {
  margin-top: 8px;
}

.caption-input {
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  color: #787774;
  background: transparent;
  text-align: center;
  outline: none;
  transition: background-color 0.15s ease;
}

.caption-input:focus {
  background: rgba(55, 53, 47, 0.04);
}

.caption-input::placeholder {
  color: #c9c7c4;
}

/* Block Actions */
.block-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  background: white;
  padding: 4px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9e9e7;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.action-btn--danger:hover {
  background: rgba(224, 62, 62, 0.1);
}

.action-icon {
  width: 14px;
  height: 14px;
  color: #787774;
}

.action-btn--danger .action-icon {
  color: #e03e3e;
}

/* Responsive */
@media (max-width: 768px) {
  .image-upload {
    padding: 24px 16px;
  }
  
  .upload-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .upload-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
