<template>
  <div 
    class="link-block"
    :class="{ 'link-block--focused': isFocused }"
  >
    <!-- URL Input -->
    <div 
      v-if="!block.content.url"
      class="link-input"
    >
      <div class="input-container">
        <LinkIcon class="input-icon" />
        <input
          ref="urlInput"
          v-model="inputUrl"
          @keydown.enter="loadLink"
          @keydown.escape="cancelInput"
          @paste="handlePaste"
          type="url"
          placeholder="Cole ou digite uma URL..."
          class="url-field"
          autofocus
        />
        <div class="input-actions">
          <button
            @click="loadLink"
            class="input-btn input-btn--primary"
            :disabled="!inputUrl.trim()"
          >
            Adicionar
          </button>
          <button
            @click="cancelInput"
            class="input-btn input-btn--secondary"
          >
            Cancelar
          </button>
        </div>
      </div>
    </div>
    
    <!-- Link Preview -->
    <div 
      v-else
      class="link-preview"
      @click="focusBlock"
    >
      <!-- Loading State -->
      <div 
        v-if="isLoadingPreview"
        class="preview-loading"
      >
        <div class="loading-spinner"></div>
        <span class="loading-text"><PERSON>egando preview...</span>
      </div>
      
      <!-- Link Card -->
      <div 
        v-else
        class="link-card"
        @click="openLink"
      >
        <div class="link-content">
          <div class="link-main">
            <div class="link-text">
              <h3 class="link-title">
                {{ block.content.title || block.content.url }}
              </h3>
              <p 
                v-if="block.content.description"
                class="link-description"
              >
                {{ block.content.description }}
              </p>
              <div class="link-url">
                <ExternalLinkIcon class="url-icon" />
                {{ formatUrl(block.content.url) }}
              </div>
            </div>
            
            <!-- Thumbnail -->
            <div 
              v-if="block.content.image"
              class="link-thumbnail"
            >
              <img
                :src="block.content.image"
                :alt="block.content.title || 'Link preview'"
                class="thumbnail-image"
                @error="handleImageError"
              />
            </div>
          </div>
          
          <!-- Favicon and Domain -->
          <div class="link-meta">
            <div class="link-domain">
              <img
                v-if="block.content.favicon"
                :src="block.content.favicon"
                :alt="getDomain(block.content.url)"
                class="domain-favicon"
                @error="hideFavicon"
              />
              <span class="domain-name">{{ getDomain(block.content.url) }}</span>
            </div>
          </div>
        </div>
        
        <!-- Hover Overlay -->
        <div class="link-overlay">
          <ExternalLinkIcon class="overlay-icon" />
        </div>
      </div>
    </div>
    
    <!-- Block Actions -->
    <div 
      v-if="isFocused"
      class="block-actions"
    >
      <button
        v-if="block.content.url"
        @click="editLink"
        class="action-btn"
        title="Editar link"
      >
        <EditIcon class="action-icon" />
      </button>
      
      <button
        v-if="block.content.url"
        @click="refreshPreview"
        class="action-btn"
        title="Atualizar preview"
      >
        <RefreshIcon class="action-icon" />
      </button>
      
      <button
        @click="duplicateBlock"
        class="action-btn"
        title="Duplicar bloco"
      >
        <CopyIcon class="action-icon" />
      </button>
      
      <button
        @click="deleteBlock"
        class="action-btn action-btn--danger"
        title="Excluir bloco"
      >
        <TrashIcon class="action-icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import {
  Link as LinkIcon,
  ExternalLink as ExternalLinkIcon,
  Edit as EditIcon,
  RefreshCw as RefreshIcon,
  Copy as CopyIcon,
  Trash2 as TrashIcon
} from 'lucide-vue-next'

export interface LinkBlockData {
  id: string
  type: 'link'
  content: {
    url: string
    title?: string
    description?: string
    image?: string
    favicon?: string
    domain?: string
  }
}

interface Props {
  block: LinkBlockData
  index?: number
}

interface Emits {
  update: [block: LinkBlockData]
  delete: [id: string]
  duplicate: [block: LinkBlockData]
  focus: [id: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const urlInput = ref<HTMLInputElement | null>(null)
const isFocused = ref(false)
const inputUrl = ref('')
const isLoadingPreview = ref(false)

// Methods
function focusBlock() {
  isFocused.value = true
  emit('focus', props.block.id)
}

async function loadLink() {
  if (!inputUrl.value.trim()) return
  
  let url = inputUrl.value.trim()
  
  // Add protocol if missing
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url
  }
  
  // Validate URL
  try {
    new URL(url)
  } catch {
    alert('URL inválida. Por favor, verifique o endereço.')
    return
  }
  
  isLoadingPreview.value = true
  
  // Update block with URL immediately
  updateBlock({
    ...props.block,
    content: {
      ...props.block.content,
      url,
      domain: getDomain(url)
    }
  })
  
  // Fetch metadata
  await fetchLinkMetadata(url)
  
  isLoadingPreview.value = false
  inputUrl.value = ''
}

async function fetchLinkMetadata(url: string) {
  try {
    // In a real application, you would call your backend API
    // For now, we'll simulate the metadata extraction
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock metadata based on domain
    const domain = getDomain(url)
    let metadata = {
      title: url,
      description: '',
      image: '',
      favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=32`
    }
    
    // Common site patterns
    if (domain.includes('github.com')) {
      metadata = {
        title: 'GitHub Repository',
        description: 'Code repository and collaboration platform',
        image: '',
        favicon: 'https://github.com/fluidicon.png'
      }
    } else if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
      metadata = {
        title: 'YouTube Video',
        description: 'Watch this video on YouTube',
        image: '',
        favicon: 'https://www.youtube.com/favicon.ico'
      }
    } else if (domain.includes('twitter.com') || domain.includes('x.com')) {
      metadata = {
        title: 'Tweet',
        description: 'View this post on X (Twitter)',
        image: '',
        favicon: 'https://abs.twimg.com/favicons/twitter.ico'
      }
    } else if (domain.includes('medium.com')) {
      metadata = {
        title: 'Medium Article',
        description: 'Read this article on Medium',
        image: '',
        favicon: 'https://miro.medium.com/v2/1*m-R_BkNf1Qjr1YbyOIJY2w.png'
      }
    }
    
    // Update block with metadata
    updateBlock({
      ...props.block,
      content: {
        ...props.block.content,
        ...metadata,
        domain
      }
    })
    
  } catch (error) {
    console.error('Error fetching metadata:', error)
    
    // Fallback metadata
    updateBlock({
      ...props.block,
      content: {
        ...props.block.content,
        title: getDomain(url),
        domain: getDomain(url),
        favicon: `https://www.google.com/s2/favicons?domain=${getDomain(url)}&sz=32`
      }
    })
  }
}

function handlePaste(event: ClipboardEvent) {
  const pastedText = event.clipboardData?.getData('text')
  if (pastedText && isValidUrl(pastedText)) {
    inputUrl.value = pastedText
    nextTick(() => {
      loadLink()
    })
  }
}

function isValidUrl(string: string): boolean {
  try {
    const url = string.startsWith('http') ? string : 'https://' + string
    new URL(url)
    return true
  } catch {
    return false
  }
}

function cancelInput() {
  emit('delete', props.block.id)
}

function editLink() {
  inputUrl.value = props.block.content.url
  updateBlock({
    ...props.block,
    content: {
      url: '',
      title: '',
      description: '',
      image: '',
      favicon: '',
      domain: ''
    }
  })
  
  nextTick(() => {
    urlInput.value?.focus()
  })
}

function refreshPreview() {
  if (props.block.content.url) {
    isLoadingPreview.value = true
    fetchLinkMetadata(props.block.content.url)
  }
}

function openLink() {
  if (props.block.content.url) {
    window.open(props.block.content.url, '_blank', 'noopener,noreferrer')
  }
}

function getDomain(url: string): string {
  try {
    return new URL(url).hostname
  } catch {
    return url
  }
}

function formatUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname + urlObj.pathname
  } catch {
    return url
  }
}

function handleImageError(event: Event) {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

function hideFavicon(event: Event) {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

function duplicateBlock() {
  emit('duplicate', props.block)
}

function deleteBlock() {
  emit('delete', props.block.id)
}

function updateBlock(updatedBlock: LinkBlockData) {
  emit('update', updatedBlock)
}

// Handle clicks outside
function handleClickOutside(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.link-block')) {
    isFocused.value = false
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  if (!props.block.content.url) {
    nextTick(() => {
      urlInput.value?.focus()
    })
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.link-block {
  position: relative;
  margin: 8px 0;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.15s ease;
}

.link-block--focused {
  background: rgba(35, 131, 226, 0.04);
}

/* URL Input */
.link-input {
  border: 2px dashed #e9e9e7;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-icon {
  width: 20px;
  height: 20px;
  color: #787774;
  flex-shrink: 0;
}

.url-field {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.15s ease;
}

.url-field:focus {
  border-color: #2383e2;
  box-shadow: 0 0 0 2px rgba(35, 131, 226, 0.1);
}

.input-actions {
  display: flex;
  gap: 8px;
}

.input-btn {
  padding: 8px 16px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.input-btn--primary {
  background: #2383e2;
  border-color: #2383e2;
  color: white;
}

.input-btn--primary:hover:not(:disabled) {
  background: #1a6cc7;
}

.input-btn--primary:disabled {
  background: #c9c7c4;
  border-color: #c9c7c4;
  cursor: not-allowed;
}

.input-btn--secondary {
  background: white;
  border-color: #e9e9e7;
  color: #37352f;
}

.input-btn--secondary:hover {
  background: #f7f6f3;
}

/* Link Preview */
.link-preview {
  position: relative;
}

.preview-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px 20px;
  color: #787774;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9e9e7;
  border-top: 2px solid #2383e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
}

/* Link Card */
.link-card {
  position: relative;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.15s ease;
  overflow: hidden;
}

.link-card:hover {
  border-color: #2383e2;
  box-shadow: 0 4px 12px rgba(35, 131, 226, 0.1);
}

.link-card:hover .link-overlay {
  opacity: 1;
}

.link-content {
  padding: 16px;
}

.link-main {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.link-text {
  flex: 1;
  min-width: 0;
}

.link-title {
  font-size: 16px;
  font-weight: 600;
  color: #37352f;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-description {
  font-size: 14px;
  color: #787774;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-url {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #9b9a97;
}

.url-icon {
  width: 12px;
  height: 12px;
}

.link-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.link-meta {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f1f1ef;
}

.link-domain {
  display: flex;
  align-items: center;
  gap: 8px;
}

.domain-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.domain-name {
  font-size: 12px;
  color: #9b9a97;
  font-weight: 500;
}

/* Hover Overlay */
.link-overlay {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background: rgba(35, 131, 226, 0.9);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.15s ease;
}

.overlay-icon {
  width: 16px;
  height: 16px;
  color: white;
}

/* Block Actions */
.block-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  background: white;
  padding: 4px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9e9e7;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.action-btn--danger:hover {
  background: rgba(224, 62, 62, 0.1);
}

.action-icon {
  width: 14px;
  height: 14px;
  color: #787774;
}

.action-btn--danger .action-icon {
  color: #e03e3e;
}

/* Responsive */
@media (max-width: 768px) {
  .link-main {
    flex-direction: column;
  }
  
  .link-thumbnail {
    width: 100%;
    height: 120px;
  }
  
  .input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .input-actions {
    justify-content: center;
  }
}
</style>
