<template>
  <div 
    class="text-block"
    :class="[`text-block--${blockType}`, { 'text-block--focused': isFocused }]"
  >
    <!-- Text Input -->
    <div
      v-if="blockType === 'paragraph' || blockType === 'heading1' || blockType === 'heading2' || blockType === 'quote'"
      ref="textElement"
      class="text-content"
      :class="`text-content--${blockType}`"
      contenteditable="true"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeyDown"
      :placeholder="getPlaceholder()"
    ></div>

    <!-- List Items -->
    <div 
      v-else-if="blockType === 'bulletList'"
      class="list-content"
    >
      <div
        v-for="(item, index) in listItems"
        :key="`item-${index}`"
        class="list-item"
      >
        <div class="list-bullet">•</div>
        <div
          :ref="el => setListItemRef(el, index)"
          class="list-item-text"
          contenteditable="true"
          @input="(e) => handleListItemInput(e, index)"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="(e) => handleListKeyDown(e, index)"
          placeholder="Digite um item..."
        >{{ item }}</div>
      </div>
    </div>

    <!-- Divider -->
    <div
      v-else-if="blockType === 'divider'"
      class="divider-content"
    >
      <hr class="divider-line" />
    </div>

    <!-- Block Actions -->
    <div 
      v-if="isFocused && blockType !== 'divider'"
      class="block-actions"
    >
      <button
        @click="changeBlockType"
        class="action-btn"
        title="Alterar tipo de bloco"
      >
        <component :is="getBlockIcon()" class="action-icon" />
      </button>
      
      <button
        @click="duplicateBlock"
        class="action-btn"
        title="Duplicar bloco"
      >
        <CopyIcon class="action-icon" />
      </button>
      
      <button
        @click="deleteBlock"
        class="action-btn action-btn--danger"
        title="Excluir bloco"
      >
        <TrashIcon class="action-icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import {
  Type as TextIcon,
  Heading1,
  Heading2,
  List,
  Quote,
  Copy as CopyIcon,
  Trash2 as TrashIcon
} from 'lucide-vue-next'

import type { BaseBlock } from '../BlocksEditor.vue'

export interface TextBlockData extends BaseBlock {
  type: 'text'
  content: {
    text: string
    subtype: 'paragraph' | 'heading1' | 'heading2' | 'bulletList' | 'quote' | 'divider'
    items?: string[]
  }
}

interface Props {
  block: TextBlockData
  index?: number
}

interface Emits {
  update: [block: TextBlockData]
  delete: [id: string]
  duplicate: [block: TextBlockData]
  createNewBlock: [afterId: string]
  focus: [id: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const textElement = ref<HTMLElement | null>(null)
const listItemRefs = ref<(HTMLElement | null)[]>([])
const isFocused = ref(false)

// Computed
const blockType = computed(() => props.block.content.subtype || 'paragraph')

const listItems = computed({
  get: () => {
    if (props.block.content.subtype === 'bulletList' && props.block.content.items) {
      return props.block.content.items
    }
    return ['']
  },
  set: (value: string[]) => {
    if (props.block.content.subtype === 'bulletList') {
      updateBlock({ 
        ...props.block, 
        content: { ...props.block.content, items: value }
      })
    }
  }
})

// Methods
function getPlaceholder(): string {
  const placeholders = {
    paragraph: 'Digite algo...',
    heading1: 'Título principal',
    heading2: 'Subtítulo',
    quote: 'Citação...'
  }
  return placeholders[blockType.value as keyof typeof placeholders] || ''
}

function getBlockIcon() {
  const icons = {
    paragraph: TextIcon,
    heading1: Heading1,
    heading2: Heading2,
    bulletList: List,
    quote: Quote
  }
  return icons[blockType.value as keyof typeof icons] || TextIcon
}

function handleInput(event: Event) {
  const target = event.target as HTMLElement
  const text = target.textContent || ''
  updateBlock({ 
    ...props.block, 
    content: { 
      ...props.block.content, 
      text 
    }
  })
}

function handleListItemInput(event: Event, index: number) {
  const target = event.target as HTMLElement
  const content = target.textContent || ''
  const newItems = [...listItems.value]
  newItems[index] = content
  listItems.value = newItems
}

function handleFocus() {
  isFocused.value = true
  emit('focus', props.block.id)
}

function handleBlur() {
  isFocused.value = false
}

function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    emit('createNewBlock', props.block.id)
  }
  
  if (event.key === 'Backspace' && textElement.value?.textContent === '') {
    event.preventDefault()
    emit('delete', props.block.id)
  }
}

function handleListKeyDown(event: KeyboardEvent, index: number) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    
    const currentContent = listItemRefs.value[index]?.textContent || ''
    
    if (currentContent === '') {
      // Exit list mode
      emit('newBlock', props.block.id, 'text')
    } else {
      // Add new list item
      const newItems = [...listItems.value]
      newItems.splice(index + 1, 0, '')
      listItems.value = newItems
      
      // Focus new item
      nextTick(() => {
        const newItemRef = listItemRefs.value[index + 1]
        if (newItemRef) {
          newItemRef.focus()
        }
      })
    }
  }
  
  if (event.key === 'Backspace') {
    const currentContent = listItemRefs.value[index]?.textContent || ''
    
    if (currentContent === '') {
      event.preventDefault()
      
      if (listItems.value.length > 1) {
        // Remove current item
        const newItems = [...listItems.value]
        newItems.splice(index, 1)
        listItems.value = newItems
        
        // Focus previous item
        const targetIndex = Math.max(0, index - 1)
        nextTick(() => {
          const targetRef = listItemRefs.value[targetIndex]
          if (targetRef) {
            targetRef.focus()
            // Set cursor at end
            const range = document.createRange()
            range.selectNodeContents(targetRef)
            range.collapse(false)
            const selection = window.getSelection()
            selection?.removeAllRanges()
            selection?.addRange(range)
          }
        })
      } else {
        // Convert to text block
        emit('newBlock', props.block.id, 'text')
        emit('delete', props.block.id)
      }
    }
  }
}

function setListItemRef(el: HTMLElement | null, index: number) {
  if (el) {
    listItemRefs.value[index] = el
  }
}

function updateBlock(updatedBlock: TextBlockData) {
  emit('update', updatedBlock)
}

function changeBlockType() {
  // TODO: Implement block type change menu
  console.log('Change block type')
}

function duplicateBlock() {
  emit('duplicate', props.block)
}

function deleteBlock() {
  emit('delete', props.block.id)
}

// Lifecycle
onMounted(() => {
  if (textElement.value && props.block.content.text) {
    textElement.value.textContent = props.block.content.text
  }
})
</script>

<style scoped>
.text-block {
  position: relative;
  margin: 4px 0;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.15s ease;
}

.text-block--focused {
  background: rgba(35, 131, 226, 0.04);
}

/* Text Content Styles */
.text-content {
  outline: none;
  word-wrap: break-word;
  white-space: pre-wrap;
  caret-color: #2383e2;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #37352f;
  line-height: 1.5;
}

.text-content--paragraph {
  font-size: 16px;
  min-height: 24px;
}

.text-content--heading1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 8px 0;
  min-height: 40px;
}

.text-content--heading2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 6px 0;
  min-height: 32px;
}

.text-content--quote {
  font-size: 16px;
  font-style: italic;
  padding-left: 16px;
  border-left: 3px solid #e9e9e7;
  color: #787774;
  min-height: 24px;
}

.text-content:empty::before {
  content: attr(placeholder);
  color: #c9c7c4;
  pointer-events: none;
}

/* List Styles */
.list-content {
  margin: 4px 0;
}

.list-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin: 2px 0;
}

.list-bullet {
  color: #787774;
  font-weight: bold;
  margin-top: 2px;
  flex-shrink: 0;
  width: 12px;
  text-align: center;
}

.list-item-text {
  flex: 1;
  outline: none;
  font-size: 16px;
  line-height: 1.5;
  color: #37352f;
  min-height: 24px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.list-item-text:empty::before {
  content: attr(placeholder);
  color: #c9c7c4;
  pointer-events: none;
}

/* Divider Styles */
.divider-content {
  margin: 16px 0;
}

.divider-line {
  border: none;
  border-top: 1px solid #e9e9e7;
  margin: 0;
}

/* Block Actions */
.block-actions {
  position: absolute;
  top: 4px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transform: translateY(-4px);
  transition: all 0.15s ease;
  background: white;
  padding: 4px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9e9e7;
}

.text-block--focused .block-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background: rgba(55, 53, 47, 0.08);
}

.action-btn--danger:hover {
  background: rgba(224, 62, 62, 0.1);
}

.action-icon {
  width: 14px;
  height: 14px;
  color: #787774;
}

.action-btn--danger .action-icon {
  color: #e03e3e;
}

/* Responsive */
@media (max-width: 768px) {
  .text-content--heading1 {
    font-size: 1.75rem;
  }
  
  .text-content--heading2 {
    font-size: 1.25rem;
  }
  
  .block-actions {
    position: static;
    opacity: 1;
    transform: none;
    margin-top: 8px;
    justify-content: flex-end;
    background: transparent;
    box-shadow: none;
    border: none;
    padding: 0;
  }
}
</style>
