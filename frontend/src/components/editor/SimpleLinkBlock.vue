<template>
  <div class="link-block">
    <!-- URL Input State -->
    <div v-if="!block.content.url" class="url-input-area">
      <div class="input-header">
        <span class="input-icon">🔗</span>
        <span class="input-title">Adicionar Link</span>
      </div>
      <div class="input-container">
        <input
          v-model="linkUrl"
          @keydown.enter="loadLink"
          @paste="handlePaste"
          placeholder="Cole ou digite uma URL..."
          class="url-input"
          type="url"
        />
        <button @click="loadLink" :disabled="!linkUrl.trim()" class="load-btn">
          ➡️
        </button>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-else-if="isLoading" class="loading-state">
      <div class="loading-spinner">⏳</div>
      <span>Carregando preview...</span>
    </div>
    
    <!-- Link Preview -->
    <div v-else class="link-preview">
      <a :href="block.content.url" target="_blank" rel="noopener" class="preview-link">
        <div class="preview-content">
          <!-- Favicon and URL -->
          <div class="preview-header">
            <span class="favicon">🌐</span>
            <span class="domain">{{ getDomain(block.content.url) }}</span>
          </div>
          
          <!-- Title and Description -->
          <div class="preview-body">
            <h4 class="preview-title">
              {{ block.content.title || 'Link sem título' }}
            </h4>
            <p v-if="block.content.description" class="preview-description">
              {{ block.content.description }}
            </p>
            <p class="preview-url">{{ block.content.url }}</p>
          </div>
        </div>
      </a>
      
      <!-- Link Actions -->
      <div class="link-actions">
        <button @click="editLink" class="action-btn">
          ✏️ Editar
        </button>
        <button @click="refreshPreview" class="action-btn">
          🔄 Atualizar
        </button>
        <button @click="removeLink" class="action-btn danger">
          🗑️ Remover
        </button>
      </div>
    </div>
    
    <!-- Edit State -->
    <div v-if="isEditing" class="edit-overlay">
      <div class="edit-modal">
        <h4>Editar Link</h4>
        <input
          v-model="editUrl"
          @keydown.enter="saveEdit"
          placeholder="URL do link..."
          class="edit-input"
          type="url"
        />
        <div class="edit-actions">
          <button @click="saveEdit" class="save-btn">✅ Salvar</button>
          <button @click="cancelEdit" class="cancel-btn">❌ Cancelar</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface LinkBlock {
  id: string
  type: 'link'
  content: {
    url: string
    title: string
    description: string
  }
}

interface Props {
  block: LinkBlock
}

interface Emits {
  update: [block: LinkBlock]
  delete: [id: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const linkUrl = ref('')
const isLoading = ref(false)
const isEditing = ref(false)
const editUrl = ref('')

async function loadLink() {
  if (!linkUrl.value.trim()) return
  
  try {
    // Validate URL
    const url = new URL(linkUrl.value)
    if (!['http:', 'https:'].includes(url.protocol)) {
      throw new Error('URL deve começar com http:// ou https://')
    }
    
    isLoading.value = true
    
    // Simulate metadata loading (in real app, call API)
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const domain = url.hostname
    const mockData = {
      url: linkUrl.value,
      title: `${domain.charAt(0).toUpperCase() + domain.slice(1)} - Site`,
      description: `Conteúdo do site ${domain}. Esta é uma descrição simulada do link.`
    }
    
    emit('update', {
      ...props.block,
      content: mockData
    })
    
    linkUrl.value = ''
    
  } catch (error) {
    alert('URL inválida. Por favor, verifique o endereço.')
  } finally {
    isLoading.value = false
  }
}

function handlePaste(event: ClipboardEvent) {
  // Auto-load on paste
  setTimeout(() => {
    if (linkUrl.value && isValidUrl(linkUrl.value)) {
      loadLink()
    }
  }, 100)
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

function getDomain(url: string): string {
  try {
    return new URL(url).hostname.replace('www.', '')
  } catch {
    return 'link'
  }
}

function editLink() {
  editUrl.value = props.block.content.url
  isEditing.value = true
}

function saveEdit() {
  if (!editUrl.value.trim()) return
  
  linkUrl.value = editUrl.value
  isEditing.value = false
  
  // Reset content and reload
  emit('update', {
    ...props.block,
    content: {
      url: '',
      title: '',
      description: ''
    }
  })
  
  // Load new URL
  setTimeout(() => {
    loadLink()
  }, 100)
}

function cancelEdit() {
  isEditing.value = false
  editUrl.value = ''
}

async function refreshPreview() {
  linkUrl.value = props.block.content.url
  
  // Reset and reload
  emit('update', {
    ...props.block,
    content: {
      url: '',
      title: '',
      description: ''
    }
  })
  
  setTimeout(() => {
    loadLink()
  }, 100)
}

function removeLink() {
  emit('delete', props.block.id)
}
</script>

<style scoped>
.link-block {
  margin: 15px 0;
  position: relative;
}

/* URL Input */
.url-input-area {
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  padding: 20px;
  background: #f7fafc;
}

.input-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.input-icon {
  font-size: 1.5rem;
}

.input-title {
  font-weight: 600;
  color: #4a5568;
}

.input-container {
  display: flex;
  gap: 8px;
}

.url-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  outline: none;
  font-size: 0.95rem;
}

.url-input:focus {
  border-color: #4299e1;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
}

.load-btn {
  background: #4299e1;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
}

.load-btn:hover:not(:disabled) {
  background: #3182ce;
}

.load-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  color: #4a5568;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Link Preview */
.link-preview {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.preview-link {
  display: block;
  text-decoration: none;
  color: inherit;
  transition: background-color 0.2s;
}

.preview-link:hover {
  background: #f7fafc;
}

.preview-content {
  padding: 15px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.favicon {
  font-size: 1.2rem;
}

.domain {
  font-size: 0.85rem;
  color: #718096;
  font-weight: 500;
}

.preview-body {
  margin-bottom: 10px;
}

.preview-title {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.3;
}

.preview-description {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-url {
  margin: 0;
  font-size: 0.8rem;
  color: #718096;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* Link Actions */
.link-actions {
  display: flex;
  gap: 8px;
  padding: 10px 15px;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

.action-btn {
  background: white;
  border: 1px solid #e2e8f0;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.action-btn:hover {
  background: #edf2f7;
}

.action-btn.danger:hover {
  background: #fed7d7;
  color: #e53e3e;
  border-color: #feb2b2;
}

/* Edit Modal */
.edit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-modal {
  background: white;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
}

.edit-modal h4 {
  margin: 0 0 15px 0;
  color: #2d3748;
}

.edit-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  outline: none;
  margin-bottom: 15px;
}

.edit-input:focus {
  border-color: #4299e1;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
}

.edit-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.save-btn {
  background: #48bb78;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
}

.cancel-btn {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
}

.save-btn:hover {
  background: #38a169;
}

.cancel-btn:hover {
  background: #cbd5e0;
}
</style>
