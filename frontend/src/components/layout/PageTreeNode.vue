<template>
  <div class="page-tree-node">
    <div 
      class="page-item"
      :class="{ 
        active: isActive,
        'has-children': hasChildren
      }"
      :style="{ paddingLeft: `${level * 12 + 8}px` }"
      @click="handleSelect"
      @mouseover="showActions = true"
      @mouseleave="showActions = false"
    >
      <!-- Expand/Collapse Button -->
      <button 
        v-if="hasChildren"
        class="expand-btn"
        @click.stop="toggleExpanded"
      >
        <ChevronRightIcon 
          class="expand-icon" 
          :class="{ expanded: isExpanded }"
        />
      </button>
      
      <div v-else class="expand-spacer"></div>

      <!-- Page Icon -->
      <div class="page-icon">
        {{ getPageIcon(page) }}
      </div>

      <!-- Page Title -->
      <span class="page-title">{{ page.title || 'Sem título' }}</span>

      <!-- Action Buttons -->
      <div v-show="showActions" class="page-actions">
        <button 
          class="action-btn"
          @click.stop="$emit('create-child', page)"
          title="Criar subpágina"
        >
          <PlusIcon class="action-icon" />
        </button>
        
        <button 
          class="action-btn"
          @click.stop="showMenu = !showMenu"
          title="Mais opções"
        >
          <MoreHorizontalIcon class="action-icon" />
        </button>
      </div>

      <!-- Context Menu -->
      <div v-if="showMenu" class="context-menu" @click.stop>
        <button 
          class="menu-item"
          @click="duplicatePage"
        >
          <CopyIcon class="menu-icon" />
          Duplicar
        </button>
        
        <button 
          class="menu-item"
          @click="movePage"
        >
          <MoveIcon class="menu-icon" />
          Mover
        </button>
        
        <div class="menu-separator"></div>
        
        <button 
          class="menu-item danger"
          @click="deletePage"
        >
          <TrashIcon class="menu-icon" />
          Excluir
        </button>
      </div>
    </div>

    <!-- Children -->
    <div v-if="hasChildren && isExpanded" class="page-children">
      <PageTreeNode
        v-for="child in page.children"
        :key="child.id"
        :page="child"
        :level="level + 1"
        :current-page-id="currentPageId"
        @select="$emit('select', $event)"
        @create-child="$emit('create-child', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { Page } from '@/stores/pages'
import { 
  ChevronRight as ChevronRightIcon,
  Plus as PlusIcon,
  MoreHorizontal as MoreHorizontalIcon,
  Copy as CopyIcon,
  Move3D as MoveIcon,
  Trash2 as TrashIcon
} from 'lucide-vue-next'

interface Props {
  page: Page
  level: number
  currentPageId?: number | null
}

interface Emits {
  select: [page: Page]
  'create-child': [page: Page]
  delete: [page: Page]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isExpanded = ref(false)
const showActions = ref(false)
const showMenu = ref(false)

// Computed
const hasChildren = computed(() => 
  props.page.children && props.page.children.length > 0
)

const isActive = computed(() => 
  props.currentPageId === props.page.id
)

// Methods
function handleSelect() {
  emit('select', props.page)
}

function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}

function getPageIcon(page: Page): string {
  // Simple icon logic - could be expanded
  if (hasChildren.value) {
    return '📁'
  }
  
  // Could check page content or type for different icons
  return '📄'
}

function duplicatePage() {
  // TODO: Implement page duplication
  console.log('Duplicate page:', props.page.title)
  showMenu.value = false
}

function movePage() {
  // TODO: Implement page moving
  console.log('Move page:', props.page.title)
  showMenu.value = false
}

function deletePage() {
  emit('delete', props.page)
  showMenu.value = false
}

// Click outside handler for context menu
function handleClickOutside(event: MouseEvent) {
  if (!event.target) return
  
  const target = event.target as Element
  if (!target.closest('.context-menu') && !target.closest('.action-btn')) {
    showMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Auto-expand if this page or a child is active
if (props.page.children?.some(child => child.id === props.currentPageId)) {
  isExpanded.value = true
}
</script>

<style scoped>
.page-tree-node {
  position: relative;
}

.page-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  position: relative;
  min-height: 28px;
}

.page-item:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

.page-item.active {
  background-color: rgba(35, 131, 226, 0.15);
  color: #2383e2;
  font-weight: 500;
}

.page-item.active .page-title {
  color: #2383e2;
}

/* Expand Button */
.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border: none;
  background: none;
  border-radius: 2px;
  cursor: pointer;
  padding: 0;
}

.expand-btn:hover {
  background-color: rgba(55, 53, 47, 0.1);
}

.expand-spacer {
  width: 18px;
  height: 18px;
}

.expand-icon {
  width: 12px;
  height: 12px;
  color: #787774;
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* Page Icon */
.page-icon {
  font-size: 14px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Page Title */
.page-title {
  flex: 1;
  font-size: 14px;
  color: #37352f;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* Actions */
.page-actions {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: auto;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  border-radius: 3px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.15s ease;
}

.action-btn:hover {
  background-color: rgba(55, 53, 47, 0.1);
  opacity: 1;
}

.action-icon {
  width: 12px;
  height: 12px;
  color: #787774;
}

/* Context Menu */
.context-menu {
  position: absolute;
  top: 100%;
  right: 8px;
  background: white;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 140px;
  padding: 4px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 6px 12px;
  border: none;
  background: none;
  text-align: left;
  font-size: 14px;
  color: #37352f;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.menu-item:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

.menu-item.danger {
  color: #e03e3e;
}

.menu-item.danger:hover {
  background-color: rgba(224, 62, 62, 0.1);
}

.menu-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.menu-separator {
  height: 1px;
  background-color: #e9e9e7;
  margin: 4px 0;
}

/* Children */
.page-children {
  /* No additional styling needed as the level padding is handled in page-item */
}
</style>
