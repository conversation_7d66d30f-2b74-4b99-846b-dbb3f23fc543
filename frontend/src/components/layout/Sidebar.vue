<template>
  <div class="sidebar-container">
    <!-- Header -->
    <div class="sidebar-header">
      <div class="workspace-info">
        <div class="workspace-icon">📝</div>
        <div class="workspace-details">
          <h3>Alt-Notion</h3>
          <span class="workspace-subtitle">Personal</span>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="sidebar-nav">
      <!-- Quick Actions -->
      <div class="nav-section">
        <button 
          class="nav-item create-page-btn"
          @click="createNewPage"
          :disabled="pageStore.loading"
        >
          <PlusIcon class="nav-icon" />
          <span>Nova Página</span>
        </button>
      </div>

      <!-- Pages Tree -->
      <div class="nav-section">
        <div class="section-header">
          <h4>Páginas</h4>
        </div>
        
        <div v-if="pageStore.loading" class="loading-state">
          <div class="loading-spinner"></div>
          <span>Carregando...</span>
        </div>
        
        <div v-else-if="pageStore.error" class="error-state">
          <span>{{ pageStore.error }}</span>
          <button @click="pageStore.loadPages()">Tentar novamente</button>
        </div>
        
        <div v-else class="pages-tree">
          <PageTreeNode
            v-for="page in pageStore.pageTree"
            :key="page.id"
            :page="page"
            :level="0"
            @select="selectPage"
            @create-child="createChildPage"
            @delete="deletePage"
          />
          
          <div v-if="pageStore.pageTree.length === 0" class="empty-state">
            <p>Nenhuma página encontrada</p>
            <button @click="createNewPage" class="create-first-page">
              Criar primeira página
            </button>
          </div>
        </div>
      </div>

      <!-- Settings -->
      <div class="nav-section nav-bottom">
        <router-link to="/settings" class="nav-item">
          <SettingsIcon class="nav-icon" />
          <span>Configurações</span>
        </router-link>
        
        <button class="nav-item" @click="toggleSidebar">
          <SidebarIcon class="nav-icon" />
          <span>Ocultar Sidebar</span>
        </button>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue'
import { useRouter } from 'vue-router'
import { usePageStore } from '@/stores/pages'
import type { Page } from '@/stores/pages'
import PageTreeNode from './PageTreeNode.vue'

// Icons (we'll create these or use lucide-vue-next)
import { 
  Plus as PlusIcon,
  Settings as SettingsIcon,
  PanelLeftClose as SidebarIcon
} from 'lucide-vue-next'

const router = useRouter()
const pageStore = usePageStore()

// Inject sidebar state
const sidebarCollapsed = inject<any>('sidebarCollapsed')

// Methods
async function createNewPage() {
  try {
    const newPage = await pageStore.createPage({
      title: 'Nova Página',
      content: '',
      parent_id: null
    })
    
    // Navigate to the new page
    router.push(`/pages/${newPage.id}`)
  } catch (error) {
    console.error('Error creating page:', error)
  }
}

async function createChildPage(parentPage: Page) {
  try {
    const newPage = await pageStore.createPage({
      title: 'Nova Subpágina',
      content: '',
      parent_id: parentPage.id
    })
    
    router.push(`/pages/${newPage.id}`)
  } catch (error) {
    console.error('Error creating child page:', error)
  }
}

function selectPage(page: Page) {
  pageStore.setCurrentPage(page)
  router.push(`/pages/${page.id}`)
}

async function deletePage(page: Page) {
  if (confirm(`Tem certeza que deseja excluir "${page.title}" e todas suas subpáginas?`)) {
    try {
      await pageStore.deletePage(page.id)
      
      // If we're currently viewing the deleted page, redirect to home
      if (pageStore.currentPage?.id === page.id) {
        router.push('/')
      }
    } catch (error) {
      console.error('Error deleting page:', error)
    }
  }
}

function toggleSidebar() {
  if (sidebarCollapsed) {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}
</script>

<style scoped>
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f7f6f3;
}

/* Header */
.sidebar-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e9e9e7;
}

.workspace-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.workspace-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
}

.workspace-details h3 {
  font-size: 14px;
  font-weight: 600;
  color: #37352f;
  margin: 0;
}

.workspace-subtitle {
  font-size: 12px;
  color: #787774;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 8px 0;
  display: flex;
  flex-direction: column;
}

.nav-section {
  padding: 0 12px;
  margin-bottom: 16px;
}

.nav-section.nav-bottom {
  margin-top: auto;
  margin-bottom: 8px;
  padding-top: 8px;
  border-top: 1px solid #e9e9e7;
}

.section-header h4 {
  font-size: 12px;
  font-weight: 500;
  color: #787774;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0;
  padding: 0 8px;
}

/* Nav Items */
.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 6px 8px;
  border-radius: 4px;
  border: none;
  background: none;
  color: #37352f;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.nav-item:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

.nav-item.router-link-active {
  background-color: rgba(55, 53, 47, 0.12);
  font-weight: 500;
}

.nav-icon {
  width: 16px;
  height: 16px;
  color: #787774;
  flex-shrink: 0;
}

.create-page-btn {
  font-weight: 500;
  color: #2383e2;
}

.create-page-btn:hover {
  background-color: rgba(35, 131, 226, 0.1);
}

.create-page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading and Error States */
.loading-state, .error-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  color: #787774;
  font-size: 14px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e9e9e7;
  border-top: 2px solid #2383e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-state button {
  background: none;
  border: 1px solid #e9e9e7;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  color: #37352f;
}

.error-state button:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 24px 16px;
  color: #787774;
}

.empty-state p {
  margin: 0 0 12px 0;
  font-size: 14px;
}

.create-first-page {
  background: #2383e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.create-first-page:hover {
  background: #1a6cc7;
}

/* Pages Tree */
.pages-tree {
  margin-top: 4px;
}
</style>
