<template>
  <Teleport to="body">
    <div 
      v-if="modelValue" 
      class="modal-backdrop" 
      @click="handleBackdropClick"
      role="dialog"
      aria-modal="true"
      :aria-labelledby="titleId"
    >
      <div 
        :class="[
          'modal-container',
          `modal-size-${size}`,
          { 'modal-fullscreen': fullscreen }
        ]"
        @click.stop
        ref="modalRef"
      >
        <slot />
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface Props {
  modelValue: boolean
  size?: 'small' | 'medium' | 'large' | 'xl'
  fullscreen?: boolean
  closeOnBackdrop?: boolean
  titleId?: string
}

interface Emits {
  'update:modelValue': [value: boolean]
  'close': []
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  fullscreen: false,
  closeOnBackdrop: true,
  titleId: 'modal-title'
})

const emit = defineEmits<Emits>()

const modalRef = ref<HTMLElement>()

// Handle backdrop click
function handleBackdropClick() {
  if (props.closeOnBackdrop) {
    close()
  }
}

// Close modal
function close() {
  emit('update:modelValue', false)
  emit('close')
}

// Handle escape key
function handleEscape(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.modelValue) {
    close()
  }
}

// Focus management
function focusModal() {
  nextTick(() => {
    if (modalRef.value) {
      // Try to focus first focusable element inside modal
      const focusable = modalRef.value.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement
      
      if (focusable) {
        focusable.focus()
      } else {
        modalRef.value.focus()
      }
    }
  })
}

// Store previous active element for focus restoration
let previousActiveElement: HTMLElement | null = null

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
  
  // Restore focus when modal is unmounted
  if (previousActiveElement) {
    previousActiveElement.focus()
  }
})

// Watch for modal open/close
watch(() => props.modelValue, (isOpen) => {
  if (isOpen) {
    // Store current active element
    previousActiveElement = document.activeElement as HTMLElement
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden'
    
    // Focus modal
    focusModal()
  } else {
    // Restore body scroll
    document.body.style.overflow = ''
    
    // Restore focus
    if (previousActiveElement) {
      previousActiveElement.focus()
      previousActiveElement = null
    }
  }
})
</script>

<style scoped>
/* Backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 15, 15, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: backdrop-fade-in 0.2s ease;
}

@keyframes backdrop-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Container */
.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  max-height: 90vh;
  width: 100%;
  position: relative;
  animation: modal-slide-in 0.3s ease;
  outline: none;
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Sizes */
.modal-size-small {
  max-width: 400px;
}

.modal-size-medium {
  max-width: 600px;
}

.modal-size-large {
  max-width: 800px;
}

.modal-size-xl {
  max-width: 1200px;
}

.modal-fullscreen {
  max-width: none;
  max-height: none;
  height: 100vh;
  width: 100vw;
  border-radius: 0;
  margin: 0;
}

/* Responsive */
@media (max-width: 640px) {
  .modal-backdrop {
    padding: 0;
  }
  
  .modal-container:not(.modal-fullscreen) {
    max-width: none;
    width: 100vw;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
  }
}
</style>
