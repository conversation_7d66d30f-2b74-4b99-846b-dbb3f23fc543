<template>
  <div class="share-modal">
    <header class="modal-header">
      <h2>Compartilhar Página</h2>
      <button @click="$emit('close')" class="close-btn">
        <XIcon class="close-icon" />
      </button>
    </header>
    
    <div class="modal-body">
      <div class="page-info">
        <div class="page-icon">📄</div>
        <div>
          <h3>{{ page.title || 'Sem título' }}</h3>
          <p class="page-meta">Criada em {{ formatDate(page.created_at) }}</p>
        </div>
      </div>
      
      <!-- Existing Shares -->
      <div v-if="shares.length > 0" class="existing-shares">
        <h4>Links de compartilhamento ativos</h4>
        <div v-for="share in shares" :key="share.id" class="share-item">
          <div class="share-info">
            <div class="share-url">
              <input 
                :value="share.share_url" 
                readonly 
                class="url-input"
                @click="selectUrl"
              />
              <button @click="copyUrl(share.share_url)" class="copy-btn">
                <CopyIcon class="copy-icon" />
              </button>
            </div>
            <div class="share-stats">
              <span class="views">{{ share.current_views }} visualizações</span>
              <span v-if="share.expires_at" class="expiry">
                Expira em {{ formatDate(share.expires_at) }}
              </span>
            </div>
          </div>
          <div class="share-actions">
            <button 
              @click="toggleShareActive(share)" 
              :class="{ active: share.is_active }"
              class="toggle-btn"
            >
              {{ share.is_active ? 'Ativo' : 'Inativo' }}
            </button>
            <button @click="deleteShare(share)" class="delete-btn">
              <TrashIcon class="delete-icon" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Create New Share -->
      <div class="create-share">
        <h4>{{ shares.length > 0 ? 'Criar novo link' : 'Compartilhar esta página' }}</h4>
        <p class="share-description">
          Qualquer pessoa com o link poderá visualizar esta página.
        </p>
        
        <div class="share-options">
          <div class="option-group">
            <label class="option-label">
              <input 
                type="checkbox" 
                v-model="shareSettings.allowComments"
                class="option-checkbox"
              />
              <span>Permitir comentários</span>
            </label>
          </div>
          
          <div class="option-group">
            <label class="option-label">
              <input 
                type="checkbox" 
                v-model="shareSettings.passwordProtected"
                class="option-checkbox"
              />
              <span>Proteger com senha</span>
            </label>
            <input 
              v-if="shareSettings.passwordProtected"
              v-model="shareSettings.password"
              type="password"
              placeholder="Digite a senha"
              class="password-input"
            />
          </div>
          
          <div class="option-group">
            <label class="option-label">
              <input 
                type="checkbox" 
                v-model="shareSettings.hasExpiration"
                class="option-checkbox"
              />
              <span>Definir data de expiração</span>
            </label>
            <input 
              v-if="shareSettings.hasExpiration"
              v-model="shareSettings.expirationDays"
              type="number"
              min="1"
              max="365"
              placeholder="Dias até expirar"
              class="expiration-input"
            />
          </div>
          
          <div class="option-group">
            <label class="option-label">
              <input 
                type="checkbox" 
                v-model="shareSettings.hasViewLimit"
                class="option-checkbox"
              />
              <span>Limitar número de visualizações</span>
            </label>
            <input 
              v-if="shareSettings.hasViewLimit"
              v-model.number="shareSettings.maxViews"
              type="number"
              min="1"
              placeholder="Máximo de visualizações"
              class="views-input"
            />
          </div>
          
          <div class="option-group">
            <label class="option-label-full">Descrição do compartilhamento</label>
            <textarea 
              v-model="shareSettings.description"
              placeholder="Adicione uma descrição opcional..."
              class="description-input"
            ></textarea>
          </div>
        </div>
        
        <div class="create-actions">
          <button @click="createShare" :disabled="creating" class="create-btn">
            {{ creating ? 'Criando...' : 'Gerar Link de Compartilhamento' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Toast Notification -->
    <div v-if="showToast" class="toast" :class="{ success: toastType === 'success', error: toastType === 'error' }">
      {{ toastMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { X as XIcon, Copy as CopyIcon, Trash as TrashIcon } from 'lucide-vue-next'
import type { Page, Share } from '@/stores/pages'

interface Props {
  page: Page
}

interface Emits {
  close: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const shares = ref<Share[]>([])
const creating = ref(false)
const loading = ref(false)

// Toast
const showToast = ref(false)
const toastMessage = ref('')
const toastType = ref<'success' | 'error'>('success')

// Share settings
const shareSettings = ref({
  allowComments: false,
  passwordProtected: false,
  password: '',
  hasExpiration: false,
  expirationDays: 30,
  hasViewLimit: false,
  maxViews: 100,
  description: ''
})

// Methods
async function loadShares() {
  loading.value = true
  try {
const response = await fetch(`http://localhost:5001/api/shares/?page_id=${props.page.id}`)
    if (response.ok) {
      shares.value = await response.json()
    }
  } catch (error) {
    console.error('Error loading shares:', error)
  } finally {
    loading.value = false
  }
}

async function createShare() {
  creating.value = true
  
  const shareData: any = {
    page_id: props.page.id,
    allow_comments: shareSettings.value.allowComments,
    password_protected: shareSettings.value.passwordProtected,
    description: shareSettings.value.description || null
  }
  
  if (shareSettings.value.passwordProtected && shareSettings.value.password) {
    shareData.password_hash = shareSettings.value.password
  }
  
  if (shareSettings.value.hasExpiration) {
    shareData.expires_in_days = shareSettings.value.expirationDays
  }
  
  if (shareSettings.value.hasViewLimit) {
    shareData.max_views = shareSettings.value.maxViews
  }
  
  try {
const response = await fetch('http://localhost:5001/api/shares/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(shareData)
    })
    
    if (response.ok) {
      const newShare = await response.json()
      shares.value.push(newShare)
      
      // Reset form
      Object.assign(shareSettings.value, {
        allowComments: false,
        passwordProtected: false,
        password: '',
        hasExpiration: false,
        expirationDays: 30,
        hasViewLimit: false,
        maxViews: 100,
        description: ''
      })
      
      // Copy URL to clipboard
      await copyUrl(newShare.share_url)
      showToastMessage('Link criado e copiado para área de transferência!', 'success')
    } else {
      const error = await response.json()
      showToastMessage(error.error || 'Erro ao criar link', 'error')
    }
  } catch (error) {
    console.error('Error creating share:', error)
    showToastMessage('Erro de conexão', 'error')
  } finally {
    creating.value = false
  }
}

async function toggleShareActive(share: Share) {
  try {
const response = await fetch(`http://localhost:5001/api/shares/${share.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        is_active: !share.is_active
      })
    })
    
    if (response.ok) {
      const updatedShare = await response.json()
      const index = shares.value.findIndex(s => s.id === share.id)
      if (index >= 0) {
        shares.value[index] = updatedShare
      }
      showToastMessage(`Link ${updatedShare.is_active ? 'ativado' : 'desativado'}`, 'success')
    }
  } catch (error) {
    console.error('Error toggling share:', error)
    showToastMessage('Erro ao atualizar link', 'error')
  }
}

async function deleteShare(share: Share) {
  if (!confirm('Tem certeza que deseja excluir este link de compartilhamento?')) {
    return
  }
  
  try {
    const response = await fetch(`http://localhost:5001/api/shares/${share.id}`, {
      method: 'DELETE'
    })
    
    if (response.ok) {
      shares.value = shares.value.filter(s => s.id !== share.id)
      showToastMessage('Link excluído', 'success')
    }
  } catch (error) {
    console.error('Error deleting share:', error)
    showToastMessage('Erro ao excluir link', 'error')
  }
}

async function copyUrl(url: string) {
  try {
    await navigator.clipboard.writeText(url)
    showToastMessage('Link copiado para área de transferência!', 'success')
  } catch (error) {
    // Fallback for browsers without clipboard API
    const textArea = document.createElement('textarea')
    textArea.value = url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    showToastMessage('Link copiado!', 'success')
  }
}

function selectUrl(event: Event) {
  const input = event.target as HTMLInputElement
  input.select()
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('pt-BR')
}

function showToastMessage(message: string, type: 'success' | 'error') {
  toastMessage.value = message
  toastType.value = type
  showToast.value = true
  
  setTimeout(() => {
    showToast.value = false
  }, 3000)
}

// Lifecycle
onMounted(() => {
  loadShares()
})
</script>

<style scoped>
.share-modal {
  position: relative;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9e9e7;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #37352f;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.close-btn:hover {
  background-color: rgba(55, 53, 47, 0.08);
}

.close-icon {
  width: 20px;
  height: 20px;
  color: #787774;
}

/* Body */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Page Info */
.page-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f7f6f3;
  border-radius: 8px;
  margin-bottom: 24px;
}

.page-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
}

.page-info h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #37352f;
}

.page-meta {
  margin: 2px 0 0 0;
  font-size: 0.9rem;
  color: #787774;
}

/* Existing Shares */
.existing-shares {
  margin-bottom: 32px;
}

.existing-shares h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #37352f;
}

.share-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e9e9e7;
  border-radius: 8px;
  margin-bottom: 12px;
}

.share-info {
  flex: 1;
  margin-right: 16px;
}

.share-url {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.url-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  background: #f7f6f3;
  font-size: 14px;
  color: #37352f;
  font-family: monospace;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e9e9e7;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.copy-btn:hover {
  background: #f7f6f3;
  border-color: #2383e2;
}

.copy-icon {
  width: 16px;
  height: 16px;
  color: #787774;
}

.share-stats {
  display: flex;
  gap: 16px;
  font-size: 0.85rem;
  color: #787774;
}

.share-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-btn {
  padding: 6px 12px;
  border: 1px solid #e9e9e7;
  background: white;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.toggle-btn.active {
  background: #22c55e;
  color: white;
  border-color: #22c55e;
}

.toggle-btn:not(.active):hover {
  background: #f7f6f3;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e9e9e7;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.delete-btn:hover {
  background: #fee;
  border-color: #e03e3e;
}

.delete-icon {
  width: 16px;
  height: 16px;
  color: #e03e3e;
}

/* Create Share */
.create-share h4 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #37352f;
}

.share-description {
  margin: 0 0 20px 0;
  color: #787774;
  font-size: 0.9rem;
}

.share-options {
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-label, .option-label-full {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #37352f;
  cursor: pointer;
}

.option-label-full {
  font-weight: 500;
  margin-bottom: 4px;
  cursor: default;
}

.option-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.password-input, .expiration-input, .views-input, .description-input {
  padding: 8px 12px;
  border: 1px solid #e9e9e7;
  border-radius: 6px;
  font-size: 14px;
  margin-left: 24px;
  width: calc(100% - 24px);
}

.description-input {
  resize: vertical;
  min-height: 60px;
  margin-left: 0;
  width: 100%;
}

.create-actions {
  display: flex;
  justify-content: flex-end;
}

.create-btn {
  background: #2383e2;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.create-btn:hover:not(:disabled) {
  background: #1a6cc7;
}

.create-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Toast */
.toast {
  position: absolute;
  bottom: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  animation: toast-in 0.3s ease;
}

.toast.success {
  background: #22c55e;
}

.toast.error {
  background: #e03e3e;
}

@keyframes toast-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
